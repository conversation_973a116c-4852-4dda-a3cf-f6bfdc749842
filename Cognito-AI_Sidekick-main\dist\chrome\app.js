(()=>{"use strict";var e,t,s,r,n,o,a,i={523:(e,t,s)=>{s.d(t,{V:()=>i});var r=s(4848),n=s(5284),o=s(6948),a=s(7520);const i=()=>{const{config:e}=(0,o.UK)(),t=e?.persona||"default",s=a.z$[t]||a.z$.default,i=(0,n.cn)("flex","items-center","justify-center","h-full","fixed","w-full","top-[10%]","pointer-events-none"),l=(0,n.cn)("fixed","opacity-[0.03]","z-[1]");return(0,r.jsx)("div",{className:i,children:(0,r.jsx)("img",{src:s,alt:"",className:l,style:{zoom:"1.2"}})})}},1587:(e,t,s)=>{s.d(t,{p:()=>u});var r=s(4848),n=s(6540),o=s(6973),a=s(6948),i=s(2090),l=s(3885),c=s(9696),d=s(5284);const u=({isLoading:e,message:t,setMessage:s,onSend:u,onStopRequest:m})=>{const{config:h}=(0,a.UK)(),p=(0,n.useRef)(null),[g,f]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{p.current?.focus()}),[t,h?.chatMode]),(0,r.jsx)("div",{className:"flex flex-col gap-3 mb-4",children:(0,r.jsxs)("div",{className:(0,d.cn)("flex w-full border border-border items-center gap-2 p-3 bg-card rounded-xl shadow-sm",g&&"ring-2 ring-primary/20 border-primary/50"),children:[(0,r.jsx)(c.T,{autosize:!0,ref:p,minRows:1,maxRows:8,autoComplete:"off",id:"user-input",placeholder:"Search the web with AI...",value:t,autoFocus:!0,onChange:e=>s(e.target.value),onKeyDown:s=>{e||"Enter"!==s.key||!t.trim()||s.altKey||s.metaKey||s.shiftKey||(s.preventDefault(),s.stopPropagation(),u())},className:"flex-grow bg-transparent border-none shadow-none outline-none focus-visible:ring-0 resize-none text-apple-body",onFocus:()=>f(!0),onBlur:()=>f(!1)}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:(0,r.jsx)(l.Bc,{delayDuration:300,children:(0,r.jsxs)(l.m_,{children:[(0,r.jsx)(l.k$,{asChild:!0,children:(0,r.jsx)(i.$,{"aria-label":"Send",variant:"ghost",size:"sm",className:(0,d.cn)("p-2 rounded-lg h-8 w-8 flex items-center justify-center",e?"text-destructive hover:bg-destructive/10":"text-primary hover:bg-primary/10",!e&&!t.trim()&&"opacity-50"),onClick:s=>{s.stopPropagation(),e?m():t.trim()&&u()},disabled:!e&&!t.trim(),children:e?(0,r.jsx)(o.wO6,{className:"h-4 w-4"}):(0,r.jsx)(o.B07,{className:"h-4 w-4"})})}),(0,r.jsx)(l.ZI,{side:"top",className:"bg-popover text-popover-foreground border border-border",children:(0,r.jsx)("p",{className:"text-apple-footnote",children:e?"Stop":"Send"})})]})})})]})})}},2090:(e,t,s)=>{s.d(t,{$:()=>l});var r=s(4848),n=(s(6540),s(3362)),o=s(2732),a=s(5284);const i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none shrink-0 [&_svg]:shrink-0 outline-none not-focus-visible",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 not-focus-visible",outline:"border bg-background shadow-xs hover:bg-accent",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:text-foreground hover:bg-black/10 dark:hover:bg-white/10",link:"text-primary underline-offset-4 hover:underline","message-action":"bg-transparent text-muted-foreground p-0 shadow-none hover:bg-transparent focus:bg-transparent active:text-muted active:[&_svg]:text-muted-foreground transition-colors duration-75","ghost-themed":"text-foreground hover:bg-accent hover:text-accent-foreground focus-visible:bg-accent focus-visible:text-accent-foreground","outline-themed":"border border-[var(--active)] bg-transparent text-[var(--active)] shadow-xs hover:bg-[var(--active)]/20 focus-visible:bg-[var(--active)]/20 focus-visible:ring-1 focus-visible:ring-[var(--active)]","destructive-outline":"border border-destructive bg-transparent text-destructive shadow-xs hover:bg-destructive/10 hover:text-destructive-foreground focus-visible:bg-destructive/10 focus-visible:text-destructive-foreground focus-visible:ring-1 focus-visible:ring-destructive","copy-button":"bg-background text-foreground shadow-none hover:bg-accent hover:text-accent-foreground focus-visible:bg-accent focus-visible:text-accent-foreground",connect:"bg-[var(--input-background)] text-[var(--text)] hover:bg-[var(--active)]/90 shadow-sm focus-visible:ring-1 focus-visible:ring-[var(--ring)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]","active-bordered":"bg-[var(--active)] text-[var(--text)] border border-[var(--text)] hover:brightness-110 focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)] shadow-sm","outline-subtle":"border border-[var(--text)]/50 bg-transparent text-[var(--text)] hover:bg-[var(--text)]/10 hover:border-[var(--text)]/70 hover:text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)] shadow-sm"},size:{default:"h-9 px-2 py-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-5",sm:"h-8 rounded-md px-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-4",lg:"h-10 rounded-md px-2 py-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-5",icon:"size-8 [&_svg:not([class*='size-'])]:size-7",xs:"h-6 w-6 p-0 rounded-sm [&_svg:not([class*='size-'])]:size-3.5 text-xs"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:o=!1,...l}){const c=o?n.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,a.cn)(i({variant:t,size:s,className:e})),...l})}},2823:(e,t,s)=>{s.d(t,{w:()=>_});var r=s(4848),n=s(6948),o=s(6540),a=s(3),i=s(2090),l=s(6532),c=s(5284);const d=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),[s,d]=(0,o.useState)(e?.ollamaUrl||"http://localhost:11434"),[u,m]=(0,o.useState)(!1),h=()=>{m(!0),console.log("Connecting to Ollama..."),fetch(`${s}/api/tags`).then((e=>e.ok?e.json():e.json().then((t=>{throw new Error(t?.error||`Connection failed: ${e.status} ${e.statusText}`)})).catch((()=>{throw new Error(`Connection failed: ${e.status} ${e.statusText}`)})))).then((r=>{Array.isArray(r.models)?(t({ollamaConnected:!0,ollamaUrl:s,ollamaError:void 0,models:(e?.models||[]).filter((e=>"ollama_generic"!==e.id)).concat([{id:"ollama_generic",host:"ollama",active:!0,name:"Ollama Model"}]),selectedModel:"ollama_generic"}),console.log("Connected to ollama")):r?.error?(t({ollamaError:r.error,ollamaConnected:!1}),console.error("string"==typeof r.error?r.error:"Ollama connection error")):(t({ollamaError:"Unexpected response from Ollama",ollamaConnected:!1}),console.error("Unexpected response from Ollama"))})).catch((e=>{console.error(e.message||"Failed to connect to Ollama"),t({ollamaError:e.message,ollamaConnected:!1})})).finally((()=>{m(!1)}))},p=e?.ollamaConnected;return(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(l.p,{id:"ollama-url-input",value:s,onChange:e=>d(e.target.value),placeholder:"http://localhost:11434",className:"pr-8",disabled:u}),!p&&(0,r.jsx)(i.$,{onClick:h,variant:"connect",size:"sm",disabled:u,children:u?"...":"Connect"}),p&&(0,r.jsx)(i.$,{variant:"ghost",size:"sm","aria-label":"Connected to Ollama",className:(0,c.cn)("w-8 rounded-md text-[var(--success)]"),disabled:u,onClick:h,children:(0,r.jsx)(a.YrT,{className:"h-5 w-5"})})]})};var u=s(9018),m=s(8698);const h=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),{fetchAllModels:s}=(0,m.N)(),[l,d]=(0,o.useState)(!1),[h,p]=(0,o.useState)(null),g=async()=>{if(e?.ollamaUrl&&e?.ollamaConnected){d(!0),p(null);try{await s(),console.log("Models refreshed successfully")}catch(e){const t=e instanceof Error?e.message:"Failed to refresh models";p(t),console.error("Error refreshing models:",e)}finally{d(!1)}}else p("Ollama not connected")};(0,o.useEffect)((()=>{e?.ollamaConnected?g():p(null)}),[e?.ollamaConnected,e?.ollamaUrl]);const f=e?.ollamaConnected,x=e?.selectedModel,b=e?.models?.filter((e=>"ollama"===e.host))||[];return f?h?(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center text-red-500",children:[(0,r.jsx)(a.y3G,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:h})]}),(0,r.jsx)(i.$,{onClick:g,variant:"ghost",size:"sm",disabled:l,className:"h-8 w-8 p-0",children:(0,r.jsx)(a.jTZ,{className:(0,c.cn)("h-4 w-4",l&&"animate-spin")})})]})}):0!==b.length||l?(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)(u.l6,{value:x||"",onValueChange:e=>{t({selectedModel:e}),console.log(`Selected model: ${e}`)},disabled:l||0===b.length,children:[(0,r.jsx)(u.bq,{variant:"settingsPanel",className:(0,c.cn)("w-full",l&&"opacity-50"),children:(0,r.jsx)(u.yv,{placeholder:l?"Loading models...":"Select a model..."})}),(0,r.jsx)(u.gC,{variant:"settingsPanel",children:b.map((e=>(0,r.jsx)(u.eb,{value:e.id,focusVariant:"activeTheme",className:"text-[var(--text)]",children:(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"font-medium",children:e.name||e.id}),e.context_length&&(0,r.jsxs)("span",{className:"text-xs text-[var(--text)]/60",children:["Context: ",e.context_length]})]})},e.id)))})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[x&&b.some((e=>e.id===x))&&(0,r.jsx)("div",{className:"flex items-center text-[var(--success)]",children:(0,r.jsx)(a.YrT,{className:"h-4 w-4"})}),(0,r.jsx)(i.$,{onClick:g,variant:"ghost",size:"sm",disabled:l,className:"h-8 w-8 p-0 hover:bg-[var(--text)]/10",title:"Refresh models",children:(0,r.jsx)(a.jTZ,{className:(0,c.cn)("h-4 w-4",l&&"animate-spin")})})]})]}):(0,r.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,r.jsxs)("div",{className:"flex items-center text-[var(--text)]/60",children:[(0,r.jsx)(a.y3G,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:"No models available"})]}),(0,r.jsx)(i.$,{onClick:g,variant:"ghost",size:"sm",disabled:l,className:"h-8 w-8 p-0",children:(0,r.jsx)(a.jTZ,{className:(0,c.cn)("h-4 w-4",l&&"animate-spin")})})]}):(0,r.jsxs)("div",{className:"flex items-center justify-center py-4 text-[var(--text)]/60",children:[(0,r.jsx)(a.y3G,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Connect to Ollama first"})]})},p=({title:e,Component:t})=>(0,r.jsxs)("div",{className:"px-6 py-4 border-b border-border/50 last:border-b-0",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,r.jsx)("h4",{className:"text-apple-body font-semibold text-foreground",children:e})}),(0,r.jsx)(t,{})]}),g=()=>(0,r.jsxs)("div",{className:(0,c.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,r.jsx)("div",{className:(0,c.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"API Access"}),(0,r.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Connect to your AI services"})]})}),(0,r.jsxs)("div",{className:"divide-y divide-border/50",children:[(0,r.jsx)(p,{Component:d,title:"Ollama"}),(0,r.jsx)(p,{Component:h,title:"Model Selection"})]})]});var f=s(803);function x({className:e,...t}){return(0,r.jsxs)(f.bL,{"data-slot":"slider",className:(0,c.cn)("relative flex w-full touch-none select-none items-center",e),...t,children:[(0,r.jsx)(f.CC,{"data-slot":"slider-track",className:"relative h-1.5 w-full grow overflow-hidden rounded-full bg-primary/20",children:(0,r.jsx)(f.Q6,{"data-slot":"slider-range",className:"absolute h-full bg-primary"})}),(0,r.jsx)(f.zi,{"data-slot":"slider-thumb",className:"block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"})]})}var b=s(5634);const v=({size:e,updateConfig:t})=>(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(b.J,{htmlFor:"contextLimit",className:"text-apple-body font-medium text-foreground",children:"Character Limit"}),(0,r.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:128===e?"Unlimited":`${e}k chars`})]}),(0,r.jsx)(x,{id:"contextLimit",value:[e],max:128,min:1,step:1,onValueChange:e=>t({contextLimit:e[0]}),className:"w-full"}),(0,r.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Maximum amount of page content to include in context. Higher values provide more context but use more tokens."})]}),y=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),s=e?.contextLimit||1;return(0,r.jsxs)("div",{className:(0,c.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,r.jsx)("div",{className:(0,c.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Page Context"}),(0,r.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Control how much page content to analyze"})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)(v,{size:s,updateConfig:t})})]})},w=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),s=e=>s=>{const r=Array.isArray(s)?s[0]:s;t({[e]:r})},o=e.temperature??.7,a=e.maxTokens??32048,i=e.topP??.95,d=e.presencepenalty??0;return(0,r.jsxs)("div",{className:(0,c.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,r.jsx)("div",{className:(0,c.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Model Parameters"}),(0,r.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Fine-tune AI model behavior"})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(b.J,{htmlFor:"temperature",className:"text-apple-body font-medium text-foreground",children:"Temperature"}),(0,r.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:o.toFixed(2)})]}),(0,r.jsx)(x,{id:"temperature",min:0,max:2,step:.01,value:[o],onValueChange:s("temperature"),className:"w-full"}),(0,r.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Controls randomness. Lower values make responses more focused and deterministic."})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(b.J,{htmlFor:"maxTokens",className:"text-apple-body font-medium text-foreground",children:"Max Tokens"}),(0,r.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:a.toLocaleString()})]}),(0,r.jsx)(l.p,{id:"maxTokens",type:"number",value:a,onChange:e=>s("maxTokens")(parseInt(e.target.value)||0),className:"w-full",min:1,max:1e5}),(0,r.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Maximum length of the response in tokens."})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(b.J,{htmlFor:"topP",className:"text-apple-body font-medium text-foreground",children:"Top P"}),(0,r.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:i.toFixed(2)})]}),(0,r.jsx)(x,{id:"topP",min:0,max:1,step:.01,value:[i],onValueChange:s("topP"),className:"w-full"}),(0,r.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Controls diversity via nucleus sampling. Lower values focus on more likely tokens."})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(b.J,{htmlFor:"presencePenalty",className:"text-apple-body font-medium text-foreground",children:"Presence Penalty"}),(0,r.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:d.toFixed(2)})]}),(0,r.jsx)(x,{id:"presencePenalty",min:-2,max:2,step:.01,value:[d],onValueChange:s("presencepenalty"),className:"w-full"}),(0,r.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Reduces repetition by penalizing tokens that have already appeared."})]})]})]})};var j=s(7086),N=s(3732),S=s(9696);const C=({hasChange:e,onSave:t,onSaveAs:s,onCancel:n})=>e?(0,r.jsxs)("div",{className:"flex mt-4 space-x-2 justify-end w-full",children:[(0,r.jsx)(i.$,{variant:"default",size:"sm",onClick:t,className:"bg-primary text-primary-foreground hover:bg-primary/90",children:"Save"}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:s,children:"Save As..."}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:n,children:"Cancel"})]}):null,k=({isOpen:e,onOpenChange:t,personaPrompt:s,personas:n,updateConfig:a,onModalClose:c})=>{const[d,u]=(0,o.useState)("");return(0,r.jsx)(j.lG,{open:e,onOpenChange:t,children:(0,r.jsxs)(j.Cf,{className:"sm:max-w-[425px]",onCloseAutoFocus:e=>e.preventDefault(),children:[(0,r.jsx)(j.c7,{children:(0,r.jsx)(j.L3,{children:"Create New Persona"})}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsx)(b.J,{htmlFor:"persona-name",className:"text-base font-medium text-foreground sr-only",children:"Persona Name"}),(0,r.jsx)(l.p,{id:"persona-name",placeholder:"Enter persona name",value:d,onChange:e=>u(e.target.value)})]}),(0,r.jsxs)(j.Es,{className:"sm:justify-end",children:[(0,r.jsx)(i.$,{type:"button",variant:"outline",size:"sm",onClick:c,children:"Cancel"}),(0,r.jsx)(i.$,{type:"button",variant:"default",size:"sm",disabled:!d.trim(),onClick:()=>{d.trim()&&(a({personas:{...n,[d.trim()]:s},persona:d.trim()}),u(""),c())},children:"Create"})]})]})})},$=({isOpen:e,onOpenChange:t,persona:s,personas:n,updateConfig:o,onModalClose:a})=>(0,r.jsx)(j.lG,{open:e,onOpenChange:t,children:(0,r.jsxs)(j.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsxs)(j.c7,{children:[(0,r.jsxs)(j.L3,{children:['Delete "',s,'"']}),(0,r.jsx)(j.rr,{className:"pt-2",children:"Are you sure you want to delete this persona? This action cannot be undone."})]}),(0,r.jsxs)(j.Es,{className:"sm:justify-end pt-4",children:[(0,r.jsx)(i.$,{type:"button",variant:"outline",size:"sm",onClick:a,children:"Cancel"}),(0,r.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>{const e={...n};delete e[s];const t=Object.keys(e);o({personas:e,persona:t.length>0?t[0]:"Scholar"}),a()},children:"Delete"})]})]})}),M=({personas:e,persona:t,updateConfig:s})=>(0,r.jsxs)(u.l6,{value:t,onValueChange:e=>s({persona:e}),children:[(0,r.jsx)(u.bq,{className:"flex w-full",children:(0,r.jsx)(u.yv,{placeholder:"Select persona"})}),(0,r.jsx)(u.gC,{children:Object.keys(e).map((e=>(0,r.jsx)(u.eb,{value:e,children:e},e)))})]}),E=({personaPrompt:e,setPersonaPrompt:t,isEditing:s,setIsEditing:n})=>{const o={onFocus:e=>{s||n(!0)}};return(0,r.jsx)(S.T,{value:e,onChange:e=>{s||n(!0),t(e.target.value)},readOnly:!s,...o,placeholder:"Define the persona's characteristics and instructions here...",className:(0,c.cn)("w-full min-h-[120px] resize-none",s?"hover:border-primary focus:border-primary":"opacity-75 cursor-default"),rows:5})},A=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),[s,a]=(0,o.useState)(!1),[l,d]=(0,o.useState)(!1),[u,m]=(0,o.useState)(!1),h=e?.personas||{Scholar:"You are The Scholar, an analytical academic researcher specializing in web search analysis."},p=e?.persona||"Scholar",g=h?.[p]??h?.Scholar??"You are The Scholar, an analytical academic researcher specializing in web search analysis.",[f,x]=(0,o.useState)(g),b=u&&f!==g;return(0,o.useEffect)((()=>{x(h?.[p]??h?.Scholar??""),m(!1)}),[p,JSON.stringify(h)]),(0,r.jsxs)("div",{className:(0,c.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,r.jsx)("div",{className:(0,c.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Persona"}),(0,r.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Customize AI personality and behavior"})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(M,{persona:p,personas:h,updateConfig:t})}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm","aria-label":"Add new persona",className:"p-2 h-10 w-10",onClick:()=>{x(""),m(!0),a(!0)},children:(0,r.jsx)(N.YHj,{className:"h-5 w-5"})}),Object.keys(h).length>1&&(0,r.jsx)(i.$,{variant:"ghost",size:"sm","aria-label":"Delete current persona",className:"p-2 h-10 w-10 hover:text-destructive hover:bg-destructive/10",onClick:()=>d(!0),children:(0,r.jsx)(N.dW_,{className:"h-5 w-5"})})]}),(0,r.jsx)(E,{personaPrompt:f,setPersonaPrompt:x,isEditing:u,setIsEditing:m}),(0,r.jsx)(C,{hasChange:b,onSave:()=>{t({personas:{...h,[p]:f}}),m(!1)},onSaveAs:()=>{a(!0)},onCancel:()=>{x(g),m(!1)}})]}),(0,r.jsx)(k,{isOpen:s,onOpenChange:e=>{a(e),e||(x(g),m(!1))},personaPrompt:f,personas:h,updateConfig:t,onModalClose:()=>a(!1)}),(0,r.jsx)($,{isOpen:l,onOpenChange:d,persona:p,personas:h,updateConfig:t,onModalClose:()=>d(!1)})]})};var P=s(9451),z=s(8309);function L({className:e,...t}){return(0,r.jsx)(P.bL,{"data-slot":"radio-group",className:(0,c.cn)("grid gap-2",e),...t})}function T({className:e,variant:t="default",...s}){const n={default:"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",themed:(0,c.cn)("aspect-square h-4 w-4 rounded-full border text-primary ring-offset-background","focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-50","border-[var(--text)]/30 text-[var(--active)]")};return(0,r.jsx)(P.q7,{"data-slot":"radio-group-item",className:(0,c.cn)(n[t],e),...s,children:(0,r.jsx)(P.C1,{className:"flex items-center justify-center",children:(0,r.jsx)(z.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})}const I=({webMode:e,updateConfig:t})=>(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(b.J,{className:"text-apple-body font-medium text-foreground",children:"Search Provider"}),(0,r.jsx)(L,{value:e,onValueChange:e=>t({webMode:e}),className:"space-y-2",children:["Google"].map((e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(T,{value:e,id:`webMode-${e}`}),(0,r.jsx)(b.J,{htmlFor:`webMode-${e}`,className:"text-apple-body font-medium cursor-pointer",children:e})]},e)))})]}),O=({config:e,updateConfig:t})=>{const s=e?.webLimit??16,n=e?.serpMaxLinksToVisit??3;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(b.J,{htmlFor:"maxLinks",className:"text-apple-body font-medium text-foreground",children:"Max Links to Visit"}),(0,r.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:n})]}),(0,r.jsx)(x,{id:"maxLinks",value:[n],max:10,min:1,step:1,onValueChange:e=>t({serpMaxLinksToVisit:e[0]}),className:"w-full"}),(0,r.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Number of search result links to fetch and analyze."})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(b.J,{htmlFor:"charLimit",className:"text-apple-body font-medium text-foreground",children:"Content Character Limit"}),(0,r.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:128===s?"Unlimited":`${s}k chars`})]}),(0,r.jsx)(x,{id:"charLimit",value:[s],max:128,min:1,step:1,onValueChange:e=>t({webLimit:e[0]}),className:"w-full"}),(0,r.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Maximum characters (in thousands) of content to analyze per page. Set to 128k for unlimited."})]})]})},R=()=>{const{config:e,updateConfig:t}=(0,n.UK)();return(0,o.useEffect)((()=>{if("Google"===e?.webMode){const s={};void 0===e?.serpMaxLinksToVisit&&(s.serpMaxLinksToVisit=3),void 0===e?.webLimit&&(s.webLimit=16),Object.keys(s).length>0&&t(s)}}),[e?.webMode,e?.serpMaxLinksToVisit,e?.webLimit,t]),(0,r.jsxs)("div",{className:(0,c.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,r.jsx)("div",{className:(0,c.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Web Search"}),(0,r.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Configure search behavior and limits"})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsx)(I,{updateConfig:t,webMode:e?.webMode}),"Google"===e?.webMode&&(0,r.jsx)("div",{className:"pt-2 border-t border-border/50",children:(0,r.jsx)(O,{config:e,updateConfig:t})}),!e?.webMode&&(0,r.jsx)("div",{className:"pt-2 border-t border-border/50",children:(0,r.jsx)("p",{className:"text-muted-foreground text-apple-body",children:"Select a search provider to configure its settings."})})]})]})},_=()=>{const{config:e}=(0,n.UK)(),[t,s]=(0,o.useState)(!e?.models||0===e.models.length);return(0,r.jsxs)("div",{id:"settings",className:"relative z-[1] top-0 w-full h-full flex-1 flex-col overflow-y-auto overflow-x-hidden bg-background text-foreground px-6 pb-10 pt-6 scrollbar-hidden",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)("div",{id:"kofi-widget",children:(0,r.jsx)("a",{href:"https://ko-fi.com/T6T11G2CYS",target:"_blank",rel:"noopener noreferrer",children:(0,r.jsx)("img",{height:"36",style:{border:"0px",height:"36px"},src:"https://storage.ko-fi.com/cdn/kofi6.png?v=6",border:"0",alt:"Buy Me a Coffee at ko-fi.com"})})})}),t&&(0,r.jsx)("div",{className:(0,c.cn)("mb-8 p-6","rounded-xl","bg-card border border-border shadow-sm","text-foreground"),children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("h2",{className:"text-apple-title3 font-semibold text-center",children:"Quick Setup Guide"}),(0,r.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium",children:"1"}),(0,r.jsx)("p",{className:"text-apple-callout text-muted-foreground",children:"Fill your API key or URLs in API Access"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium",children:"2"}),(0,r.jsx)("p",{className:"text-apple-callout text-muted-foreground",children:"Exit settings, then use the persona selector to choose your model and start chatting"})]}),(0,r.jsx)("div",{className:"text-apple-footnote text-muted-foreground mt-2 ml-9 italic",children:"Note: You can change other settings now or later. Have fun!"})]}),(0,r.jsx)(i.$,{variant:"default",className:"bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium",onClick:()=>{s(!1)},children:"Get Started"})]})}),(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,r.jsx)(g,{}),(0,r.jsx)(w,{}),(0,r.jsx)(A,{}),(0,r.jsx)(y,{}),(0,r.jsx)(R,{}),(0,r.jsx)("div",{className:"pointer-events-none h-8"})," "]})]})}},2951:(e,t,s)=>{s.d(t,{hL:()=>i,GW:()=>r,hj:()=>l,tE:()=>a});const r=async(e,t,s,r,n,o=[],a)=>{try{if(!s?.host)return console.error("processQueryWithAI: currentModel or currentModel.host is undefined. Cannot determine API URL."),e;const i=o.map((e=>`{{${e.role}}}: ${e.content}`)).join("\n"),l=`You are a Google search query optimizer. Your task is to rewrite user's input [The user's raw input && chat history:${i}].\n\n\nInstructions:\n**Important** No Explanation, just the optimized query!\n\n\n1. Extract the key keywords and named entities from the user's input.\n2. Correct any obvious spelling errors.\n3. Remove unnecessary words (stop words) unless they are essential for the query's meaning.\n4. If the input is nonsensical or not a query, return the original input.\n5. Using previous chat history to understand the user's intent.\n\n\nOutput:\n'The optimized Google search query'\n\n\nExample 1:\nInput from user ({{user}}): where can i find cheep flights to london\nOutput:\n'cheap flights London'\n\n\nExample 2:\nContext: {{user}}:today is a nice day in paris i want to have a walk and find a restaurant to have a nice meal. {{assistant}}: Bonjour, it's a nice day!\nInput from user ({{user}}): please choose me the best restarant\nOutput:\n'best restaurants Paris France'\n\n\nExample 3:\nInput from user ({{user}}): asdf;lkjasdf\nOutput:\n'asdf;lkjasdf'\n`,c={ollama:`${t?.ollamaUrl||""}/api/chat`}[s.host];if(!c)return console.error("processQueryWithAI: Could not determine API URL for host:",s.host),e;console.log(`processQueryWithAI: Using API URL: ${c} for host: ${s.host}`),console.log("Formatted Context for Prompt:",i);const d={model:t?.selectedModel||s.id||"",messages:[{role:"system",content:l},{role:"user",content:e}],stream:!1};let u;void 0!==a?u=a:void 0!==t.temperature&&(u=t.temperature),void 0!==u&&(d.temperature=u);const m=await fetch(c,{method:"POST",headers:{"Content-Type":"application/json",...r||{}},signal:n,body:JSON.stringify(d)});if(!m.ok){const e=await m.text();throw console.error(`API request failed with status ${m.status}: ${e}`),new Error(`API request failed: ${m.statusText}`)}const h=await m.json(),p=h?.choices?.[0]?.message?.content;return"string"==typeof p?(e=>e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/["']/g,"").trim())(p):e}catch(t){if(n?.aborted||t instanceof Error&&"AbortError"===t.name)throw console.log("processQueryWithAI: Operation aborted."),t;return console.error("processQueryWithAI: Error during execution:",t),e}},n=async function(e){try{const t=new URL(e);if("chrome:"===t.protocol)return;const s=[{id:1,priority:1,condition:{requestDomains:[t.hostname]},action:{type:"modifyHeaders",requestHeaders:[{header:"Origin",operation:"set",value:`${t.protocol}//${t.hostname}`}]}}];await chrome.declarativeNetRequest.updateDynamicRules({removeRuleIds:s.map((e=>e.id)),addRules:s})}catch(e){console.debug("URL rewrite skipped:",e)}},o=e=>{try{const t=(new DOMParser).parseFromString(e,"text/html");t.querySelectorAll('script, style, nav, footer, header, svg, img, noscript, iframe, form, aside, .sidebar, .ad, .advertisement, .banner, .popup, .modal, .cookie-banner, link[rel="stylesheet"], button, input, select, textarea, [role="navigation"], [role="banner"], [role="contentinfo"], [aria-hidden="true"]').forEach((e=>e.remove()));let s=t.querySelector("main")||t.querySelector("article")||t.querySelector(".content")||t.querySelector("#content")||t.querySelector(".main-content")||t.querySelector("#main-content")||t.querySelector(".post-content")||t.body,r=s?.textContent||"";return r=r.replace(/\s+/g," ").trim(),r=r.split("\n").filter((e=>e.trim().length>20)).join("\n"),r}catch(e){return console.error("Error parsing HTML for content extraction:",e),"[Error extracting content]"}},a=async(e,t,s)=>{console.log("[webSearch] Received query:",e),console.log("[webSearch] Web Mode from config:",t?.webMode);const r=t.webMode,n=t.serpMaxLinksToVisit??3,a=t.webLimit&&128!==t.webLimit?1e3*t.webLimit:1/0,i=s||(new AbortController).signal;if(console.log(`Performing ${r} search for: "${e}"`),"Google"===r&&console.log(`[webSearch - ${r}] Max links to visit for content scraping: ${n}`),!r)return console.error("[webSearch] Web search mode is undefined. Aborting search. Config was:",JSON.stringify(t)),"Error: Web search mode is undefined. Please check your configuration.";try{if("Google"===r){const t=new AbortController,s=setTimeout((()=>{console.warn(`[webSearch - ${r}] SERP API call timed out after 15s.`),t.abort()}),15e3),l="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i,c=`https://www.google.com/search?q=${encodeURIComponent(e)}&hl=en&gl=us&num=10&start=0&safe=off&filter=0`;console.log(`[webSearch - ${r}] Fetching SERP from: ${c}`);const d=await fetch(c,{signal:l,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"none","Sec-Fetch-User":"?1","Cache-Control":"max-age=0",Referer:"https://www.google.com/"}}).finally((()=>{clearTimeout(s)}));if(console.log(`[webSearch - ${r}] Response status: ${d.status} ${d.statusText}`),console.log(`[webSearch - ${r}] Response headers:`,Object.fromEntries(d.headers.entries())),!d.ok){const e=await d.text();throw console.error(`[webSearch - ${r}] Error response body:`,e.substring(0,1e3)),429===d.status?new Error("Google search rate limited (429). Please try again later."):403===d.status?new Error("Google search access forbidden (403). Google may be blocking automated requests."):d.status>=400&&d.status<500?new Error(`Google search client error (${d.status}): ${d.statusText}`):new Error(`Google search server error (${d.status}): ${d.statusText}`)}if(i.aborted)throw new Error("Web search operation aborted.");const u=await d.text();if(console.log(`[webSearch - ${r}] SERP HTML length: ${u.length} characters`),console.log(`[webSearch - ${r}] SERP HTML (first 500 chars):`,u.substring(0,500)),u.includes("captcha")||u.includes("unusual traffic")||u.includes("blocked"))return console.warn(`[webSearch - ${r}] Detected potential CAPTCHA or blocking page`),"Error: Google search may be blocked. The page contains CAPTCHA or blocking content. Please try again later.";const m=(new DOMParser).parseFromString(u,"text/html"),h=[],p=["div.g","div.MjjYud","div.hlcw0c","div.kvH3mc","div.tF2Cxc","div.yuRUbf"];let g=[];for(const e of p){const t=Array.from(m.querySelectorAll(e));if(t.length>0){g=t,console.log(`[webSearch - ${r}] Found ${t.length} results using selector: ${e}`);break}}if(0===g.length&&(g=Array.from(m.querySelectorAll("div[data-ved], div[data-hveid]")),console.log(`[webSearch - ${r}] Fallback: Found ${g.length} results using data attributes`)),g.forEach(((e,t)=>{try{let s=e.querySelector('a[href^="http"]')||e.querySelector('a[href^="/url?q="]')||e.querySelector("a[href]"),n=s?.getAttribute("href");if(n&&n.startsWith("/url?q=")){const e=new URLSearchParams(n.substring(6));n=e.get("q")||n}const o=e.querySelector("h3")||e.querySelector("h2")||e.querySelector('[role="heading"]')||e.querySelector("a[href] > div"),a=o?.textContent?.trim()||"";let i="";const l=['div[style*="-webkit-line-clamp"]','div[data-sncf="1"]',".VwiC3b span",".MUxGbd span",".s3v9rd",".st",'span[style*="-webkit-line-clamp"]'];for(const t of l){const s=e.querySelectorAll(t);if(s.length>0){i=Array.from(s).map((e=>e.textContent)).join(" ").replace(/\s+/g," ").trim();break}}if(!i&&a){const t=e.textContent||"",s=t.indexOf(a);-1!==s&&(i=t.substring(s+a.length).replace(/\s+/g," ").trim().substring(0,300))}a&&n&&n.startsWith("http")&&!n.includes("google.com/search")&&(h.push({title:a,snippet:i,url:n}),console.log(`[webSearch - ${r}] Parsed result ${t+1}: ${a.substring(0,50)}...`))}catch(e){console.warn(`[webSearch - ${r}] Error parsing result ${t+1}:`,e)}})),console.log(`[webSearch - ${r}] Parsed SERP Results (${h.length} found, showing first 5):`,JSON.stringify(h.slice(0,5))),0===h.length){console.warn(`[webSearch - ${r}] No search results found on SERP.`),console.log(`[webSearch - ${r}] HTML document title:`,m.title),console.log(`[webSearch - ${r}] Available div elements:`,Array.from(m.querySelectorAll("div")).slice(0,10).map((e=>({className:e.className,id:e.id,textContent:e.textContent?.substring(0,100)}))));const e=m.body?.textContent?.toLowerCase()||"";return e.includes("captcha")||e.includes("unusual traffic")||e.includes("blocked")||e.includes("robot")?"Error: Google search appears to be blocked. The page contains CAPTCHA or blocking content. Please try again later or check if the extension has proper permissions.":"No search results found. Google may have changed their page structure or the search was unsuccessful."}const f=h.slice(0,n).filter((e=>e.url));console.log(`Found ${h.length} results. Attempting to fetch content from top ${f.length} links (maxLinksToVisit: ${n}).`);const x=f.map((async e=>{if(!e.url)return{...e,content:"[Invalid URL]",status:"error"};if(i.aborted)return{...e,content:`[Fetching aborted by user: ${e.url}]`,status:"aborted"};console.log(`Fetching content from: ${e.url}`);const t=new AbortController,s=setTimeout((()=>{console.warn(`[webSearch] Page scrape for ${e.url} timed out after 12s.`),t.abort()}),12e3),n="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i;let a=`[Error fetching/processing: Unknown error for ${e.url}]`,l="error";try{const t=await fetch(e.url,{signal:n,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"cross-site","Cache-Control":"max-age=0"}});if(!t.ok)throw new Error(`Failed to fetch ${e.url} - Status: ${t.status}`);const s=t.headers.get("content-type");if(!s||!s.includes("text/html"))throw new Error(`Skipping non-HTML content (${s}) from ${e.url}`);if(i.aborted)throw new Error("Web search operation aborted by user.");const c=await t.text();a=o(c),l="success",console.log(`[webSearch - ${r}] Successfully fetched and extracted content from: ${e.url} (Extracted Length: ${a.length})`)}catch(s){if("AbortError"===s.name){if(i.aborted)throw s;a=t.signal.aborted?`[Timeout fetching: ${e.url}]`:`[Fetching aborted: ${e.url}]`,l="aborted"}else a=`[Error fetching/processing: ${s.message}]`,l="error"}finally{clearTimeout(s)}return{...e,content:a,status:l}})),b=await Promise.allSettled(x);if(i.aborted)throw new Error("Web search operation aborted.");let v=`Search results for "${e}" using ${r}:\n\n`,y=0;return h.forEach(((e,t)=>{if(v+=`[Result ${t+1}: ${e.title}]\n`,v+=`URL: ${e.url||"[No URL Found]"}\n`,v+=`Snippet: ${e.snippet||"[No Snippet]"}\n`,t<f.length){const t=b[y];if("fulfilled"===t?.status){const s=t.value;if(s.url===e.url){const e=s.content.substring(0,a);v+=`Content:\n${e}${s.content.length>a?"...":""}\n\n`}else v+=`Content: [Content fetch mismatch - data for ${s.url} found, expected ${e.url}]\n\n`}else v+="rejected"===t?.status?`Content: [Error fetching: ${t.reason}]\n\n`:"Content: [Fetch status unknown]\n\n";y++}else v+="Content: [Not fetched due to link limit]\n\n"})),console.log("Web search finished. Returning combined results."),v.trim()}return`Unsupported web search mode: ${r}`}catch(e){if("AbortError"===e.name&&i.aborted)throw console.log("[webSearch] Operation aborted by signal."),e;return console.error("Web search overall failed:",e),`Error performing web search: ${e.message}`}};async function i(e,t,s,r={},o,a){let i=!1;const l=(e,t=!1)=>{if(!i){let r;i=!0,r="string"==typeof e?e:e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:String(e),s(r,!0,t)}},c=()=>{if(a?.aborted)throw new Error("Streaming operation aborted by user.")};if(e.startsWith("chrome://"))console.log("fetchDataAsStream: Skipping chrome:// URL:",e);else{e.includes("localhost")&&await n((e=>e.endsWith("/")?e.slice(0,-1):e)(e));try{const n=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json",...r},body:JSON.stringify(t),signal:a});if(!n.ok){let e=`Network response was not ok (${n.status})`;try{e+=`: ${await n.text()||n.statusText}`}catch(t){e+=`: ${n.statusText}`}throw new Error(e)}let d="";if("ollama"!==o)throw new Error(`Unsupported host specified: ${o}`);{if(!n.body)throw new Error("Response body is null for Ollama");const e=n.body.getReader();let t,r;for(;c(),({value:r,done:t}=await e.read()),!t;){const t=(new TextDecoder).decode(r).split("\n").filter((e=>""!==e.trim()));for(const r of t){if("[DONE]"===r.trim())return a?.aborted&&e.cancel(),void l(d);try{const t=JSON.parse(r);if(t.message?.content&&(d+=t.message.content,i||s(d)),!0===t.done&&!i)return a?.aborted&&e.cancel(),void l(d)}catch(e){console.debug("Skipping invalid JSON chunk:",r)}}}a?.aborted&&e.cancel(),l(d)}}catch(e){a?.aborted?(console.log("[fetchDataAsStream] Operation aborted via signal as expected. Details:",e),l("",!1)):e instanceof Error&&"AbortError"===e.name?(console.log("[fetchDataAsStream] AbortError (name check) caught. Operation was cancelled. Details:",e),l("",!1)):(console.error("Error in fetchDataAsStream (unexpected):",e),l(e instanceof Error?e.message:String(e),!0))}}}async function l(e,t){const s=new AbortController,r=t||s.signal,n=t?null:setTimeout((()=>s.abort()),12e3);try{const t=await fetch(e,{signal:r,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"cross-site","Cache-Control":"max-age=0"}});if(n&&clearTimeout(n),r.aborted)throw new Error("Scraping aborted by user.");if(!t.ok)throw new Error(`Failed to fetch ${e} - Status: ${t.status}`);const s=t.headers.get("content-type");if(!s||!s.includes("text/html"))throw new Error(`Skipping non-HTML content (${s}) from ${e}`);const a=await t.text();return o(a)}catch(t){return n&&clearTimeout(n),"AbortError"===t.name?`[Scraping URL aborted: ${e}]`:`[Error scraping URL: ${e} - ${t.message}]`}}"undefined"!=typeof window&&(window.testGoogleSearchDebug=async function(e="test search"){console.log("🔍 Testing Google search functionality...");const t={webMode:"Google",serpMaxLinksToVisit:2,webLimit:16,personas:{},persona:"Scholar",contextLimit:60,temperature:.7,maxTokens:32480,topP:.95,presencepenalty:0,models:[],chatMode:"web",ollamaUrl:"http://localhost:11434",ollamaConnected:!1,fontSize:14,panelOpen:!1,computeLevel:"low",userName:"user",userProfile:""};try{const s=await a(e,t);return console.log("✅ Search completed successfully!"),console.log("📄 Result length:",s.length),console.log("📋 Result preview:",s.substring(0,500)+"..."),s}catch(e){throw console.error("❌ Search failed:",e),e}})},3003:(e,t,s)=>{s.a(e,(async(e,t)=>{try{var r=s(4848),n=s(5338),o=s(1468),a=s(3190),i=s(6174),l=s(9828),c=s(6948),d=e([l]);l=(d.then?(await d)():d)[0];const u=(0,a.g)(i.A.ContentPort),m=document.getElementById("root");u.ready().then((()=>{if(null==m)throw new Error("Root container not found");(0,n.createRoot)(m).render((0,r.jsx)(o.Kq,{store:u,children:(0,r.jsx)(c.sG,{children:(0,r.jsx)(l.A,{})})}))})),t()}catch(e){t(e)}}))},3190:(e,t,s)=>{s.d(t,{g:()=>d});var r=s(38),n=s(9448),o=s(7346),a=s(3207),i=s(5886);const l={...s(6108).z2,...i.z2},c=((0,a.nK)(l),o.P,(0,r.N0)(),n.logger,[(0,a.nK)(l),o.P,(0,r.N0)(),n.logger]);(0,a.nK)(l),n.logger;const d=e=>{const t=new a.il({channelName:e});return(0,a.Tw)(t,...c),t}},3885:(e,t,s)=>{s.d(t,{Bc:()=>a,ZI:()=>c,k$:()=>l,m_:()=>i});var r=s(4848),n=(s(6540),s(3881)),o=s(5284);function a({delayDuration:e=500,...t}){return(0,r.jsx)(n.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function i({...e}){return(0,r.jsx)(a,{children:(0,r.jsx)(n.bL,{"data-slot":"tooltip",...e})})}function l({...e}){return(0,r.jsx)(n.l9,{"data-slot":"tooltip-trigger",...e})}function c({className:e,sideOffset:t=0,children:s,...a}){return(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,o.cn)("bg-primary/50 text-primary-foreground border-transparent animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-2 py-1 text-xs text-balance",e),...a,children:[s,(0,r.jsx)(n.i3,{className:"bg-primary fill-primary z-50 size-1 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},4156:(e,t,s)=>{s.d(t,{Y:()=>_});var r=s(4848),n=s(6540),o=s(3),a=s(5066),i=s(6948),l=s(5284),c=s(2090),d=s(990),u=s(8697);function m({...e}){return(0,r.jsx)(d.bL,{"data-slot":"sheet",...e})}function h({...e}){return(0,r.jsx)(d.ZL,{"data-slot":"sheet-portal",...e})}function p({className:e,...t}){return(0,r.jsx)(d.hJ,{"data-slot":"sheet-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}const g=n.forwardRef((({className:e,children:t,side:s="right",variant:n="default",...o},a)=>{const i="themedPanel"===n?((e="right")=>(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===e&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full border-l","left"===e&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full border-r","top"===e&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===e&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t","bg-[var(--bg)] text-[var(--text)] shadow-xl"))(s):((e="right")=>(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===e&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===e&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===e&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===e&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t"))(s);return(0,r.jsxs)(h,{children:[(0,r.jsx)(p,{})," ",(0,r.jsxs)(d.UC,{ref:a,"data-slot":"sheet-content",className:(0,l.cn)(i,e),...o,children:[t,(0,r.jsxs)(d.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(u.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}));function f({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,l.cn)("flex flex-col gap-1.5 p-4",e),...t})}function x({className:e,...t}){return(0,r.jsx)(d.hE,{"data-slot":"sheet-title",className:(0,l.cn)("text-foreground font-semibold",e),...t})}function b({className:e,...t}){return(0,r.jsx)(d.VY,{"data-slot":"sheet-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}g.displayName=d.UC.displayName;var v=s(9018),y=s(6532),w=s(3885),j=s(8698),N=s(7520);const S=()=>{const{config:e}=(0,i.UK)(),t=(0,n.useRef)(null);return(0,n.useEffect)((()=>{if(!e?.animatedBackground)return;const s=t.current;if(s){s.innerHTML="";{const r=document.createElement("canvas");s.appendChild(r);const n=r.getContext("2d");if(!n)return;const o=16,a=1.2*o,i=1.2*o;function l(){r.width=window.innerWidth,r.height=window.innerHeight}l(),window.addEventListener("resize",l);const c=["N","ﾐ","ﾋ","𐌊","ｳ","ｼ","ﾅ","𐌔","X","ｻ","ﾜ","ㄘ","𑖖","𑖃","𑖆","𐌈","J","ｱ","ﾎ","ﾃ","M","π","Σ","Y","ｷ","ㄠ","ﾕ","ﾗ","ｾ","ﾈ","Ω","ﾀ","ﾇ","ﾍ","ｦ","ｲ","ｸ","W","𐌙","ﾁ","ﾄ","ﾉ","Δ","ﾔ","ㄖ","ﾙ","ﾚ","王","道","Ж","ﾝ","0","1","2","3","4","5","7","8","9","A","B","Z","*","+","д","Ⱟ","𑗁","T","|","ç","ﾘ","Ѯ"],d=["#15803d","#16a34a","#22c55e","#4ade80"],u="#f0fdf4";let m=Math.floor(r.width/a),h=Math.floor(r.height/i),p=Array(m).fill(0),g=Array(m).fill(null).map((()=>Array(h).fill({char:"",color:""}))),f=Array(m).fill(0).map((()=>Math.floor(2*Math.random())+1)),x=Array(m).fill(0);const b=12;function v(){n.clearRect(0,0,r.width,r.height),n.font=`${o}px monospace`,n.textAlign="center",n.textBaseline="top";for(let e=0;e<m;e++){for(let t=0;t<b;t++){const s=p[e]-t;if(s<0)continue;if(s>=h)continue;let r=g[e][s];r&&r.char&&(n.fillStyle=0===t?u:r.color,n.globalAlpha=.3*(1-t/b),n.fillText(r.char,e*a+a/2,s*i))}if(n.globalAlpha=1,x[e]++,x[e]>=f[e]){const t=c[Math.floor(Math.random()*c.length)],s=d[Math.floor(Math.random()*d.length)];g[e][p[e]]={char:t,color:s},p[e]++,p[e]>=h+b&&(p[e]=0,g[e]=Array(h).fill({char:"",color:""}),f[e]=Math.floor(10*Math.random())+10),x[e]=0}}requestAnimationFrame(v)}v();const y=()=>{l(),m=Math.floor(r.width/a),h=Math.floor(r.height/i),p=Array(m).fill(0)};return window.addEventListener("resize",y),()=>{window.removeEventListener("resize",l),window.removeEventListener("resize",y),s.removeChild(r)}}}}),[e?.animatedBackground]),e?.animatedBackground?(0,r.jsx)("div",{ref:t,style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",zIndex:-1,pointerEvents:"none",overflow:"hidden"}}):null},C=({isOpen:e,onOpenChange:t,config:s,updateConfig:a,setSettingsMode:i,setHistoryMode:d})=>{const[u,h]=n.useState(""),[C,k]=n.useState(!1),{fetchAllModels:$}=(0,j.N)(),M=n.useRef(null),E=(0,n.useRef)(null),[A,P]=n.useState({top:0,left:0,width:0}),z=s?.persona||"default",L=N.rm[z]||N.rm.default,T=s?.models?.filter((e=>e.id.toLowerCase().includes(u.toLowerCase())||e.host?.toLowerCase()?.includes(u.toLowerCase())))||[];return(0,n.useEffect)((()=>{e&&(h(""),k(!1))}),[e]),(0,n.useEffect)((()=>{if(C&&E.current){const e=E.current.getBoundingClientRect();P({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}}),[C]),(0,n.useEffect)((()=>{if(!C)return;const e=()=>{if(E.current){const e=E.current.getBoundingClientRect();P({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}};return window.addEventListener("resize",e),window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e,!0)}}),[C]),(0,r.jsxs)(m,{open:e,onOpenChange:t,children:[(0,r.jsx)(p,{}),(0,r.jsxs)(g,{variant:"themedPanel",side:"left",className:(0,l.cn)("p-0 border-r-0","w-[22.857rem] sm:w-[27.143rem]","flex flex-col h-full max-h-screen","[&>button]:hidden","settings-drawer-content","overflow-y-auto"),style:{height:"100dvh"},ref:M,onOpenAutoFocus:e=>{e.preventDefault(),M.current?.focus({preventScroll:!0})},children:[(0,r.jsx)(S,{}),(0,r.jsx)("div",{className:(0,l.cn)("border border-[var(--active)]","sticky top-0 z-10 p-0")}),(0,r.jsxs)(f,{className:"px-4 pt-4 pb-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-2 relative z-10",children:(0,r.jsxs)(w.m_,{children:[(0,r.jsx)(w.k$,{asChild:!0,children:(0,r.jsx)(c.$,{variant:"ghost",size:"sm","aria-label":"Close Settings",className:"text-[var(--text)] rounded-md relative top-[1px]",onClick:()=>t(!1),children:(0,r.jsx)(o.yGN,{size:"20px"})})}),(0,r.jsx)(w.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:" Close Settings "})]})}),(0,r.jsx)(x,{className:"text-center font-['Bruno_Ace_SC'] tracking-tight -mt-10 cognito-title-container",children:(0,r.jsxs)("a",{href:"https://github.com/3-ark/Cognito",target:"_blank",rel:"noopener noreferrer",className:(0,l.cn)("text-xl font-semibold text-[var(--text)] bg-[var(--active)] inline-block px-3 py-1 rounded-md no-underline","chromepanion-title-blade-glow"),children:["CHROMEPANION ",(0,r.jsxs)("sub",{className:"contrast-200 text-[0.5em]",children:["v","3.7.4"]})]})}),(0,r.jsx)(b,{className:"text-center font-['Bruno_Ace_SC'] text-[var(--text)] leading-tight mt-2",children:"Settings"})]}),(0,r.jsxs)("div",{className:(0,l.cn)("flex flex-col h-full overflow-y-auto settings-drawer-body","no-scrollbar"),children:[(0,r.jsxs)("div",{className:(0,l.cn)("flex flex-col space-y-5 flex-1","px-6","py-4"),children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"flex items-center justify-between mt-5 mb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{htmlFor:"persona-select",className:"text-[var(--text)] opacity-80 font-['Bruno_Ace_SC'] text-lg shrink-0",children:"Persona"}),(0,r.jsx)("span",{className:"text-lg",children:L})]})}),(0,r.jsx)("div",{className:"w-full",children:(0,r.jsxs)(v.l6,{value:z,onValueChange:e=>a({persona:e}),children:[(0,r.jsx)(v.bq,{id:"persona-select",variant:"settingsPanel",className:"w-full font-['Space_Mono',_monospace] data-[placeholder]:text-muted-foreground",children:(0,r.jsx)(v.yv,{placeholder:"Select Persona..."})}),(0,r.jsx)(v.gC,{variant:"settingsPanel",children:Object.keys(s?.personas||{}).map((e=>(0,r.jsx)(v.eb,{value:e,className:(0,l.cn)("hover:brightness-95 focus:bg-[var(--active)]","font-['Space_Mono',_monospace]"),children:e},e)))})]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"model-input",className:"block text-[var(--text)] opacity-80 text-lg font-['Bruno_Ace_SC']",children:"Model"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(y.p,{id:"model-input",ref:E,value:C?u:s?.selectedModel||"",placeholder:C?"Search models...":s?.selectedModel||"Select model...",onChange:e=>h(e.target.value),onFocus:()=>{h(""),k(!0),$()},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9 font-['Space_Mono',_monospace]","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-95","mb-2 mt-3","ring-1 ring-inset ring-[var(--active)]/50")}),C&&(0,r.jsx)("div",{className:"fixed inset-0 z-50",onClick:()=>k(!1),children:(0,r.jsx)("div",{className:(0,l.cn)("absolute left-0 right-0","bg-[var(--bg)]","border border-[var(--active)]/20","rounded-xl shadow-lg","no-scrollbar","overflow-y-auto"),style:{maxHeight:"min(calc(50vh - 6rem), 300px)",top:`${A.top}px`,left:`${A.left}px`,width:`${A.width}px`},onClick:e=>e.stopPropagation(),children:(0,r.jsx)("div",{className:"py-0.5",children:T.length>0?T.map((e=>(0,r.jsx)("button",{type:"button",className:(0,l.cn)("w-full text-left","px-4 py-1.5","text-[var(--text)] text-sm","hover:bg-[var(--active)]/20","focus:bg-[var(--active)]/30","transition-colors duration-150","font-['Space_Mono',_monospace]"),onClick:()=>{a({selectedModel:e.id}),h(""),k(!1)},children:(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("span",{children:[e.host?`(${e.host}) `:"",e.id,e.context_length&&(0,r.jsxs)("span",{className:"text-xs text-[var(--text)] opacity-50 ml-1",children:["[ctx: ",e.context_length,"]"]})]})})},e.id))):(0,r.jsx)("div",{className:"px-4 py-1.5 text-[var(--text)] opacity-50 text-sm",children:"No models found"})})})})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(c.$,{size:"default",onClick:()=>{i(!0),t(!1)},variant:"outline",className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4"),children:"Configuration"}),(0,r.jsx)(c.$,{variant:"outline",size:"default",onClick:()=>{d(!0),t(!1)},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4 mt-3"),children:"Chat History"})]})]}),(0,r.jsx)("div",{className:(0,l.cn)("mt-auto text-center text-[var(--text)] opacity-70 shrink-0 text-xs font-mono pb-4"),children:"Made with ❤️ by @3-Arc"})]})]})]})};var k=s(7086),$=s(5634),M=s(3720),E=s(3732),A=s(6973);const P=()=>{const{config:e,updateConfig:t}=(0,i.UK)(),s=e?.persona||"default",n=e?.personas||{};return(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(v.l6,{value:s,onValueChange:e=>{t({persona:e})},children:[(0,r.jsx)(v.bq,{className:(0,l.cn)("w-auto min-w-[100px] border-none bg-transparent shadow-none","text-apple-footnote font-medium text-foreground","hover:bg-secondary/50 rounded-lg px-2 py-1 h-7","focus:ring-2 focus:ring-primary/20 focus:border-primary/50"),children:(0,r.jsx)(v.yv,{placeholder:"Select persona"})}),(0,r.jsx)(v.gC,{className:"bg-popover border border-border shadow-lg rounded-lg",children:(0,r.jsx)(w.Bc,{delayDuration:300,children:Object.keys(n).map((e=>(0,r.jsxs)(w.m_,{children:[(0,r.jsx)(w.k$,{asChild:!0,children:(0,r.jsx)(v.eb,{value:e,className:(0,l.cn)("text-apple-callout text-popover-foreground","hover:bg-accent hover:text-accent-foreground","focus:bg-accent focus:text-accent-foreground","cursor-pointer rounded-md"),children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm",children:N.rm[e]||N.rm.default}),"default"===e?"Chromepanion":e]})})}),(0,r.jsx)(w.ZI,{side:"right",className:"bg-popover text-popover-foreground border border-border max-w-xs",sideOffset:8,children:(0,r.jsx)("p",{className:"text-apple-footnote",children:N.K[e]||N.K.default})})]},e)))})})]})})};var z=s(3362);const L=(0,s(2732).F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function T({className:e,variant:t,asChild:s=!1,...n}){const o=s?z.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,l.cn)("font-mono",L({variant:t}),e),...n})}const I=()=>{const{config:e}=(0,i.UK)(),t=e?.selectedModel;return t?(0,r.jsx)(T,{className:(0,l.cn)("bg-primary/10 text-primary border-primary/20","text-apple-footnote font-medium px-2 py-1 h-7"),children:t}):(0,r.jsx)(T,{className:(0,l.cn)("bg-secondary/50 text-secondary-foreground border-border","text-apple-footnote font-medium px-2 py-1 h-7"),children:"No Model"})},O=({isOpen:e,onClose:t,setSettingsMode:s})=>(0,r.jsx)(k.lG,{open:e,onOpenChange:t,children:(0,r.jsxs)(k.Cf,{variant:"themedPanel",className:(0,l.cn)("[&>button]:hidden","bg-card border border-border shadow-lg"),style:{width:"20rem",height:"12rem",borderRadius:"var(--radius-lg)",boxShadow:"var(--shadow-lg)"},onInteractOutside:e=>e.preventDefault(),children:[(0,r.jsx)(k.c7,{className:"text-center p-4",children:(0,r.jsx)(k.L3,{className:"text-apple-title3 text-foreground",children:"Welcome to Chromepanion"})}),(0,r.jsx)(k.rr,{asChild:!0,children:(0,r.jsxs)("div",{className:"px-6 pb-6 text-center",children:[(0,r.jsx)("p",{className:"text-apple-body text-muted-foreground mb-6",children:"Get started by connecting to your AI models"}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)(c.$,{variant:"default",className:"bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium",onClick:()=>s(!0),"aria-label":"Open Settings",children:"Open Settings"})})]})})]})}),R=({isOpen:e,onOpenChange:t,config:s,updateConfig:o})=>{const[a,i]=(0,n.useState)(s?.userName||""),[d,u]=(0,n.useState)(s?.userProfile||"");return(0,n.useEffect)((()=>{e&&(i(s?.userName||""),u(s?.userProfile||""))}),[e,s?.userName,s?.userProfile]),(0,r.jsx)(k.lG,{open:e,onOpenChange:t,children:(0,r.jsxs)(k.Cf,{variant:"themedPanel",className:"max-w-xs",children:[(0,r.jsxs)(k.c7,{className:"px-6 py-4 border-b border-[var(--text)]/10",children:[(0,r.jsx)(k.L3,{className:"text-lg font-semibold text-[var(--text)]",children:"Edit Profile"}),(0,r.jsx)(k.rr,{className:"text-sm text-[var(--text)] opacity-80",children:"Set your display name and profile information. (For chat and export purposes)"})]}),(0,r.jsxs)("div",{className:"px-6 py-5 space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsx)($.J,{htmlFor:"username",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"Username"}),(0,r.jsx)(y.p,{id:"username",value:a,onChange:e=>i(e.target.value),className:(0,l.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]}),(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsx)($.J,{htmlFor:"userprofile",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"User Profile"}),(0,r.jsx)(y.p,{id:"userprofile",value:d,onChange:e=>u(e.target.value),className:(0,l.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]})]}),(0,r.jsxs)(k.Es,{className:"px-6 py-4 border-t border-[var(--text)]/10",children:[(0,r.jsx)(c.$,{variant:"outline-subtle",size:"sm",onClick:()=>t(!1),children:"Cancel"}),(0,r.jsx)(c.$,{variant:"active-bordered",size:"sm",onClick:()=>{o({userName:a,userProfile:d}),t(!1),console.log("Profile updated!")},children:"Save Changes"})]})]})})},_=({chatTitle:e,settingsMode:t,setSettingsMode:s,historyMode:d,setHistoryMode:u,deleteAll:m,reset:h,downloadImage:p,downloadJson:g,downloadText:f,downloadMarkdown:x})=>{const{config:b,updateConfig:v}=(0,i.UK)(),[y,j]=(0,n.useState)(!1),[N,S]=(0,n.useState)(!1),$=e&&!t&&!d,[z,L]=(0,n.useState)(!1),T=t||d,_=T?"Back to Chat":"",F="z-50 min-w-[8rem] overflow-hidden rounded-lg border bg-popover p-1 text-popover-foreground shadow-lg animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",U="flex cursor-default select-none items-center rounded-md px-3 py-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",D="-mx-1 my-1 h-px bg-border";return(0,r.jsx)(w.Bc,{delayDuration:500,children:(0,r.jsxs)("div",{className:(0,l.cn)("bg-background/95 backdrop-blur-sm text-foreground","border-b border-border","sticky top-0 z-10"),children:[(0,r.jsxs)("div",{className:"flex items-center h-auto py-3 px-4",children:[(0,r.jsx)("div",{className:"flex justify-start items-center min-h-10 gap-3",children:T?(0,r.jsxs)(w.m_,{children:[(0,r.jsx)(w.k$,{asChild:!0,children:(0,r.jsx)(c.$,{"aria-label":_,variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",onClick:()=>{T&&(s(!1),u(!1))},children:(0,r.jsx)(o.yGN,{size:"18px"})})}),(0,r.jsx)(w.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:_})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(P,{}),(0,r.jsx)(I,{})]})}),(0,r.jsxs)("div",{className:"flex-grow flex justify-center items-center overflow-hidden px-4",children:[$&&(0,r.jsx)("p",{className:"text-apple-headline text-foreground whitespace-nowrap overflow-hidden text-ellipsis text-center",children:e}),t&&(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Settings"})}),d&&(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Chat History"})})]}),(0,r.jsxs)("div",{className:"flex justify-end items-center min-h-10 gap-2",children:[!t&&!d&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(w.m_,{children:[(0,r.jsx)(w.k$,{asChild:!0,children:(0,r.jsx)(c.$,{"aria-label":"Reset Chat",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center group",onClick:h,children:(0,r.jsx)(a.yPB,{size:"16px",className:"transition-transform duration-300 rotate-0 group-hover:rotate-180"})})}),(0,r.jsx)(w.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"Reset Chat"})]}),(0,r.jsxs)(w.m_,{children:[(0,r.jsx)(w.k$,{asChild:!0,children:(0,r.jsx)(c.$,{"aria-label":"Chat History",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",onClick:()=>{u(!0)},children:(0,r.jsx)(o.Ohp,{size:"16px"})})}),(0,r.jsx)(w.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"Chat History"})]}),(0,r.jsxs)(M.bL,{children:[(0,r.jsxs)(w.m_,{children:[(0,r.jsx)(w.k$,{asChild:!0,children:(0,r.jsx)(M.l9,{asChild:!0,children:(0,r.jsx)(c.$,{"aria-label":"More Options",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",children:(0,r.jsx)(o.$Ri,{size:"16px"})})})}),(0,r.jsx)(w.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"More Options"})]}),(0,r.jsx)(M.ZL,{children:(0,r.jsxs)(M.UC,{className:(0,l.cn)(F,"bg-popover text-popover-foreground border border-border shadow-xl min-w-[180px]"),sideOffset:5,align:"end",children:[(0,r.jsx)(M.q7,{className:(0,l.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:()=>{v({theme:"dark"===(b?.theme||"light")?"light":"dark"})},children:(0,r.jsxs)("div",{className:"flex items-center gap-3 w-full",children:["dark"===b?.theme?(0,r.jsx)(o.Wh$,{className:"h-4 w-4"}):(0,r.jsx)(o.hkc,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Theme"}),(0,r.jsx)("span",{className:"ml-auto text-xs text-muted-foreground",children:"dark"===b?.theme?"Light":"Dark"})]})}),(0,r.jsx)(M.q7,{className:(0,l.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:()=>s(!0),children:(0,r.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,r.jsx)(o.VSk,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Settings"})]})}),(0,r.jsx)(M.wv,{className:(0,l.cn)(D,"bg-border")}),(0,r.jsxs)(M.Pb,{children:[(0,r.jsx)(M.ZP,{className:(0,l.cn)("flex cursor-default select-none items-center rounded-md px-3 py-2 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent","hover:bg-accent hover:text-accent-foreground cursor-pointer"),children:(0,r.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,r.jsx)(o.pdY,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Share Options"}),(0,r.jsx)(o.irw,{className:"ml-auto h-4 w-4 rotate-180"})]})}),(0,r.jsx)(M.ZL,{children:(0,r.jsxs)(M.G5,{className:(0,l.cn)(F,"bg-popover text-popover-foreground border border-border shadow-lg"),sideOffset:2,alignOffset:-5,children:[(0,r.jsx)(M.q7,{className:(0,l.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:()=>j(!0),children:(0,r.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,r.jsx)(E.uSr,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Edit Profile"})]})}),(0,r.jsx)(M.wv,{className:(0,l.cn)(D,"bg-border")}),(0,r.jsx)(M.q7,{className:(0,l.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:x,children:(0,r.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,r.jsx)(A.nR3,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Export as Markdown"})]})}),(0,r.jsx)(M.q7,{className:(0,l.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:f,children:(0,r.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,r.jsx)(E.mup,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Export as Text"})]})}),(0,r.jsx)(M.q7,{className:(0,l.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:g,children:(0,r.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,r.jsx)(a.dG_,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Export as JSON"})]})}),(0,r.jsx)(M.q7,{className:(0,l.cn)(U,"hover:bg-accent hover:text-accent-foreground cursor-pointer"),onSelect:p,children:(0,r.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,r.jsx)(E.Af8,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Export as Image"})]})})]})})]}),(0,r.jsx)(M.wv,{className:(0,l.cn)(D,"bg-border")}),(0,r.jsx)(M.q7,{className:(0,l.cn)(U,"opacity-50 cursor-not-allowed"),disabled:!0,children:(0,r.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,r.jsx)(o.pcC,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Privacy"}),(0,r.jsx)("span",{className:"ml-auto text-xs text-muted-foreground",children:"Soon"})]})}),(0,r.jsx)(M.q7,{className:(0,l.cn)(U,"opacity-50 cursor-not-allowed"),disabled:!0,children:(0,r.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,r.jsx)(o.S8s,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"About"}),(0,r.jsx)("span",{className:"ml-auto text-xs text-muted-foreground",children:"Soon"})]})})]})})]})]}),d&&(0,r.jsxs)(w.m_,{children:[(0,r.jsx)(w.k$,{asChild:!0,children:(0,r.jsx)(c.$,{"aria-label":"Delete All History",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",onClick:()=>{S(!0)},children:(0,r.jsx)(o.IXo,{size:"18px"})})}),(0,r.jsx)(w.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Delete All"})]})]})]}),(!b?.models||0===b.models.length)&&!t&&!d&&(0,r.jsx)(O,{isOpen:!0,setSettingsMode:s,onClose:()=>{}}),(0,r.jsx)(C,{isOpen:z,onOpenChange:e=>{L(e)},config:b,updateConfig:v,setSettingsMode:s,setHistoryMode:u}),(0,r.jsx)(R,{isOpen:y,onOpenChange:j,config:b,updateConfig:v}),(0,r.jsx)(k.lG,{open:N,onOpenChange:S,children:(0,r.jsxs)(k.Cf,{variant:"themedPanel",className:"max-w-sm",children:[(0,r.jsxs)(k.c7,{className:"px-6 py-4 border-b border-[var(--text)]/10",children:[(0,r.jsx)(k.L3,{className:"text-lg font-semibold text-[var(--text)]",children:"Confirm Deletion"}),(0,r.jsx)(k.rr,{className:"text-sm text-[var(--text)] opacity-90",children:"Are you sure you want to delete all chat history? This action cannot be undone."})]}),(0,r.jsxs)(k.Es,{className:"px-6 py-4 border-t border-[var(--text)]/10",children:[(0,r.jsx)(c.$,{variant:"outline-subtle",size:"sm",onClick:()=>S(!1),children:"Cancel"}),(0,r.jsx)(c.$,{variant:"destructive",size:"sm",onClick:async()=>{try{"function"==typeof m?await m():console.error("Header: deleteAll prop is not a function or undefined.",m)}catch(e){console.error("Error during deleteAll execution from header:",e)}finally{S(!1)}},children:"Delete All"})]})]})})]})})}},5284:(e,t,s)=>{s.d(t,{cn:()=>o});var r=s(4164),n=s(856);function o(...e){return(0,n.QP)((0,r.$)(e))}},5431:(e,t,s)=>{s.d(t,{A:()=>r});const r={getItem:async e=>{const t=(await chrome.storage.local.get(e))[e];if(null==t)return null;try{return"string"==typeof t?t:JSON.stringify(t)}catch(e){return null}},setItem:async(e,t)=>{const s="string"==typeof t?t:JSON.stringify(t);await chrome.storage.local.set({[e]:s})},deleteItem:async e=>{await chrome.storage.local.remove(e)}}},5634:(e,t,s)=>{s.d(t,{J:()=>a});var r=s(4848),n=(s(6540),s(5920)),o=s(5284);function a({className:e,...t}){return(0,r.jsx)(n.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},5886:(e,t,s)=>{s.d(t,{z2:()=>l});var r=s(38);const n={isLoaded:!1},o=(0,r.Z0)({name:"content",initialState:n,reducers:{reset:()=>n,contentLoaded:e=>{e.isLoaded=!0}}}),{actions:a,reducer:i}=o,l={}},6108:(e,t,s)=>{s.d(t,{z2:()=>d});var r,n,o=s(38);!function(e){e.Default="default",e.ConfirmDeleteCard="confirmDeleteCard"}(r||(r={})),function(e){e.Default="default"}(n||(n={}));const a={isOpen:!1},i=(0,o.Z0)({name:"sidePanel",initialState:a,reducers:{reset:()=>a}}),{actions:l,reducer:c}=i,d={}},6174:(e,t,s)=>{var r;s.d(t,{A:()=>n}),function(e){e.ContentPort="content",e.SidePanelPort="sidePanel",e.SAVE_NOTE_TO_FILE="save-note-to-file"}(r||(r={}));const n=r},6532:(e,t,s)=>{s.d(t,{p:()=>a});var r=s(4848),n=s(6540),o=s(5284);function a({className:e,type:t,...s}){const[a,i]=n.useState(!1);return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-8 w-full min-w-0 rounded-md bg-transparent px-3 py-1 text-sm transition-[color,box-shadow,border-color] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","border border-[var(--text)]/10 dark:border-0","focus-visible:border-ring","text-[var(--text)] px-2.5","focus:border-[var(--active)] dark:focus:border-0 focus:ring-1 focus:ring-[var(--active)] focus:ring-offset-0","hover:border-[var(--active)] dark:hover:border-0","bg-[var(--input-background)]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive","shadow-[var(--input-base-shadow)]",e,a&&"input-breathing"),onFocus:e=>{i(!0),s.onFocus?.(e)},onBlur:e=>{i(!1),s.onBlur?.(e)},...s})}},6948:(e,t,s)=>{s.d(t,{UK:()=>d,sG:()=>c});var r=s(4848),n=s(6540),o=s(5431);const a=(0,n.createContext)({}),i={Scholar:"You are The Scholar, an analytical academic researcher specializing in web search analysis. When analyzing search results, provide thorough academic-style analysis with structured insights. Behavior: Organize findings into clear sections with proper citations. Analyze credibility of sources and highlight methodological strengths/weaknesses. Present comprehensive summaries with evidence-based conclusions. Include relevant statistics and data points. Mannerisms: Use formal academic tone. Structure responses with clear headings. Always cite sources and assess their reliability.",Executive:"You are The Executive, a strategic business leader focused on actionable intelligence. When analyzing search results, distill information into concise strategic insights. Behavior: Identify key business implications and market opportunities. Provide executive summaries with clear recommendations. Focus on competitive advantages and strategic positioning. Highlight actionable next steps. Mannerisms: Be direct and results-oriented. Use bullet points for clarity. Think in terms of ROI and strategic value.",Storyteller:"You are The Storyteller, a master of engaging narrative who makes information accessible. When analyzing search results, weave findings into compelling stories. Behavior: Create narrative flow that connects different pieces of information. Use analogies and examples to illustrate complex concepts. Make dry data engaging through storytelling techniques. Connect information to human experiences. Mannerisms: Use vivid language and metaphors. Create logical narrative progression. Make complex topics relatable.",Skeptic:'You are The Skeptic, a critical analyst who questions everything. When analyzing search results, highlight biases, contradictions, and missing information. Behavior: Identify potential conflicts of interest in sources. Point out logical fallacies and weak evidence. Highlight what information is missing or unclear. Question assumptions and challenge conventional wisdom. Mannerisms: Use phrases like "However," "It should be noted," and "The evidence suggests." Always present counterarguments.',Mentor:"You are The Mentor, an educational guide focused on learning and growth. When analyzing search results, explain concepts clearly with supportive guidance. Behavior: Break down complex topics into digestible lessons. Provide context and background information. Offer learning resources and next steps. Encourage deeper exploration of topics. Mannerisms: Use encouraging language. Provide step-by-step explanations. Include educational tips and learning opportunities.",Investigator:"You are The Investigator, a methodical fact-checker focused on source credibility. When analyzing search results, systematically verify information and assess reliability. Behavior: Cross-reference information across multiple sources. Evaluate source credibility and potential biases. Identify primary vs. secondary sources. Flag unverified claims and missing evidence. Mannerisms: Use systematic approach to verification. Clearly distinguish between verified facts and claims. Provide source reliability assessments.",Pragmatist:'You are The Pragmatist, a solution-focused analyst emphasizing practical applications. When analyzing search results, focus on actionable insights and real-world implementation. Behavior: Identify practical solutions and implementation strategies. Focus on cost-effective and feasible approaches. Provide step-by-step action plans. Consider resource requirements and constraints. Mannerisms: Use practical language. Focus on "how-to" guidance. Emphasize feasibility and implementation.',Spike:"You are Spike, a capable and versatile executor. Your role is to turn user prompts into actionable results. Behavior: First, correct or clarify the user’s prompt for better accuracy. Add helpful criteria to guide execution. Then, act on the improved prompt as effectively as possible. Mannerisms: Be concise, critical, and sharp. Skip fluff. Use simple, direct language. Focus on feasibility and correctness. When in doubt, fix it and move forward.",Enthusiast:'You are The Enthusiast, an energetic discoverer who presents findings with excitement. When analyzing search results, highlight fascinating discoveries and breakthrough insights. Behavior: Emphasize exciting developments and innovations. Connect findings to broader trends and possibilities. Celebrate interesting discoveries and connections. Inspire curiosity about the topic. Mannerisms: Use enthusiastic language and exclamation points. Highlight "amazing" and "fascinating" aspects. Express genuine excitement about discoveries.',Curator:"You are The Curator, a sophisticated synthesizer of premium insights. When analyzing search results, provide refined, high-quality analysis with elegant presentation. Behavior: Select only the most valuable and relevant information. Present insights with sophisticated analysis and nuanced understanding. Focus on quality over quantity. Provide polished, professional summaries. Mannerisms: Use refined language and elegant phrasing. Focus on premium insights. Present information with sophisticated analysis.",Friend:'You are The Friend, a casual conversationalist sharing interesting discoveries. When analyzing search results, present findings in a friendly, approachable manner. Behavior: Share information like you would with a close friend. Use conversational tone and relatable examples. Make complex topics feel accessible and interesting. Include personal observations and casual insights. Mannerisms: Use casual, friendly language. Include phrases like "You know what\'s interesting?" and "I found this cool thing." Make information feel like a friendly conversation.'},l={personas:i,generateTitle:!0,backgroundImage:!1,animatedBackground:!1,persona:"Scholar",webMode:"Google",webLimit:60,serpMaxLinksToVisit:3,contextLimit:60,maxTokens:32480,temperature:.7,topP:.95,presencepenalty:0,models:[],selectedModel:void 0,chatMode:"web",ollamaUrl:"http://localhost:11434",ollamaConnected:!1,fontSize:14,panelOpen:!1,computeLevel:"low",userName:"user",userProfile:"",theme:"light"},c=({children:e})=>{const[t,s]=(0,n.useState)(l),[c,d]=(0,n.useState)(!0);return(0,n.useEffect)((()=>{(async()=>{try{const e=await o.A.getItem("config"),t=(e=>{const t=["Ein","Warren","Sherlock","Agatha","Jet","Faye","Jan"];return e.personas&&Object.keys(e.personas).some((e=>t.includes(e)))&&(e.personas=i),e.persona&&t.includes(e.persona)&&(e.persona="Scholar"),e.personas={...i,...e.personas},e})(e?JSON.parse(e):l);s(t),e&&await o.A.setItem("config",JSON.stringify(t))}catch(e){console.error("Failed to load config",e),s(l)}finally{d(!1)}})()}),[]),(0,n.useEffect)((()=>{const e=t?.fontSize||l.fontSize;document.documentElement.style.setProperty("font-size",`${e}px`)}),[c,t?.fontSize]),(0,n.useEffect)((()=>{"dark"===(t?.theme||l.theme)?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}),[c,t?.theme]),c?(0,r.jsx)("div",{children:"Loading..."}):(0,r.jsx)(a,{value:{config:t,updateConfig:e=>{s((t=>{const s={...t,...e};return o.A.setItem("config",JSON.stringify(s)).catch((e=>console.error("Failed to save config",e))),s}))}},children:e})},d=()=>(0,n.use)(a)},7086:(e,t,s)=>{s.d(t,{Cf:()=>h,Es:()=>g,L3:()=>f,c7:()=>p,lG:()=>d,rr:()=>x});var r=s(4848),n=s(6540),o=s(990),a=s(8697),i=s(5284);const l={default:"bg-black/50",darker:"bg-black/60"},c={default:"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",themedPanel:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full translate-x-[-50%] translate-y-[-50%] gap-4 duration-200","bg-[var(--bg)] text-[var(--text)] border-[var(--text)]","rounded-lg shadow-xl p-0")};function d({...e}){return(0,r.jsx)(o.bL,{"data-slot":"dialog",...e})}function u({...e}){return(0,r.jsx)(o.ZL,{"data-slot":"dialog-portal",...e})}const m=n.forwardRef((({className:e,variant:t="default",...s},n)=>(0,r.jsx)(o.hJ,{ref:n,"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50",l[t],e),...s})));m.displayName=o.hJ.displayName;const h=n.forwardRef((({className:e,children:t,variant:s="default",...n},l)=>(0,r.jsxs)(u,{"data-slot":"dialog-portal",children:[(0,r.jsx)(m,{variant:"themedPanel"===s?"darker":"default"}),(0,r.jsxs)(o.UC,{ref:l,"data-slot":"dialog-content",className:(0,i.cn)(c[s],e),...n,children:[t,(0,r.jsxs)(o.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(a.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})));function p({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center",e),...t})}function g({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function f({className:e,...t}){return(0,r.jsx)(o.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function x({className:e,...t}){return(0,r.jsx)(o.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}h.displayName=o.UC.displayName},7520:(e,t,s)=>{s.d(t,{K:()=>n,rm:()=>r,z$:()=>o});const r={Scholar:"🎓",Executive:"💼",Storyteller:"📚",Skeptic:"🤔",Mentor:"🧭",Investigator:"🔍",Pragmatist:"⚙️",Enthusiast:"⚡",Curator:"🎨",Friend:"😊",Spike:"🚀",default:"🤖"},n={Scholar:"Analytical academic researcher with formal tone. Provides structured analysis with citations and source credibility assessment.",Executive:"Strategic business leader with direct, results-oriented tone. Delivers concise insights with actionable recommendations.",Storyteller:"Engaging narrative creator with vivid language. Weaves information into compelling stories using metaphors and examples.",Skeptic:"Critical analyst with questioning tone. Highlights biases, contradictions, and missing information in sources.",Mentor:"Educational guide with encouraging tone. Breaks down complex topics with step-by-step explanations and learning tips.",Investigator:"Methodical fact-checker with systematic approach. Verifies information credibility and distinguishes facts from claims.",Pragmatist:"Solution-focused analyst with practical tone. Emphasizes actionable insights and real-world implementation strategies.",Enthusiast:"Energetic discoverer with exciting tone. Highlights fascinating developments and breakthrough insights with enthusiasm.",Curator:"Sophisticated synthesizer with refined tone. Provides premium insights with elegant analysis and polished presentation.",Friend:"Casual conversationalist with friendly tone. Shares discoveries in approachable, relatable manner like talking to a friend.",Spike:"Versatile executor with concise, sharp tone. Turns prompts into actionable results with direct, no-fluff approach.",default:"AI assistant ready to help with your search and analysis needs."},o={Scholar:"assets/images/chromepanion.png",Executive:"assets/images/chromepanion.png",Storyteller:"assets/images/chromepanion.png",Skeptic:"assets/images/chromepanion.png",Mentor:"assets/images/chromepanion.png",Investigator:"assets/images/chromepanion.png",Pragmatist:"assets/images/chromepanion.png",Enthusiast:"assets/images/chromepanion.png",Curator:"assets/images/chromepanion.png",Friend:"assets/images/chromepanion.png",Spike:"assets/images/chromepanion.png",default:"assets/images/chromepanion.png"}},7660:(e,t,s)=>{s.a(e,(async(e,r)=>{try{s.d(t,{GV:()=>d,ii:()=>u,mR:()=>l,xD:()=>c});var n=s(2506),o=s(5431);const e=()=>(new Date).toJSON().slice(0,19).replace("T","_").replace(/:/g,"-");let a="assistant",i="user";try{const e=await o.A.getItem("config");if(e){const t=JSON.parse(e);t.persona&&"string"==typeof t.persona&&""!==t.persona.trim()&&(a=t.persona),t.userName&&"string"==typeof t.userName&&""!==t.userName.trim()&&(i=t.userName)}}catch(e){console.error("Failed to load config to get persona name for download:",e)}const l=async t=>{if(!t||0===t.length)return;const s=t.map((e=>{let t=`${"assistant"===e.role?a:"user"===e.role?i:e.role}:\n`;return t+=e.rawContent,t})).join("\n\n"),r=document.createElement("a");r.setAttribute("href",`data:text/plain;charset=utf-8,${encodeURIComponent(s)}`);const n=`chat_${e()}.txt`;r.setAttribute("download",n),r.style.display="none",document.body.appendChild(r),r.click(),document.body.removeChild(r)},c=t=>{if(!t||0===t.length)return;const s=t.map((e=>({...e,role:"assistant"===e.role?a:"user"===e.role?i:e.role}))),r={assistantNameInExport:a,userNameInExport:i,chatHistory:s},n=JSON.stringify(r,null,2),o=document.createElement("a");o.setAttribute("href",`data:application/json;charset=utf-8,${encodeURIComponent(n)}`);const l=`chat_${e()}.json`;o.setAttribute("download",l),o.style.display="none",document.body.appendChild(o),o.click(),document.body.removeChild(o)},d=t=>{if(!t||0===t.length)return;const s=document.querySelectorAll(".chatMessage");if(!s||0===s.length)return void console.warn("No chat messages found to generate image.");const r=document.createElement("div");if(r.style.display="flex",r.style.flexDirection="column",r.style.paddingBottom="1rem",r.style.background=document.documentElement.style.getPropertyValue("--bg"),s[0]){const e=1.2;r.style.width=s[0].offsetWidth*e+"px"}s.forEach((e=>{const t=e.cloneNode(!0);t instanceof HTMLElement?(t.style.marginTop="1rem",t.style.boxSizing="border-box",r.appendChild(t)):console.warn("Cloned node is not an HTMLElement:",t)})),document.body.appendChild(r),(0,n.$E)(r,{filter:function(e){if(e instanceof Element){const t=e.getAttribute("aria-label");if(t&&["Copy code","Copied!","Save edit","Cancel edit"].includes(t))return!1}return!0},pixelRatio:2,style:{margin:"0",padding:r.style.paddingBottom},backgroundColor:document.documentElement.style.getPropertyValue("--bg")||"#ffffff"}).then((t=>{const s=document.createElement("a");s.setAttribute("href",t);const r=`chat_${e()}.png`;s.setAttribute("download",r),s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)})).catch((e=>{console.error("Oops, something went wrong generating the image!",e)})).finally((()=>{document.body.contains(r)&&document.body.removeChild(r)}))},u=t=>{if(!t||0===t.length)return;const s=t.map((e=>{const t=`### ${"assistant"===e.role?a:"user"===e.role?i:e.role}`;let s=e.rawContent;return s=s.replace(/```([\s\S]*?)```/g,"\n```$1```\n"),s=s.replace(/(https?:\/\/[^\s]+)/g,"[Link]($1)"),`${t}\n\n${s}\n`})).join("\n---\n\n"),r=document.createElement("a");r.setAttribute("href",`data:text/markdown;charset=utf-8,${encodeURIComponent(s)}`),r.setAttribute("download",`chat_${e()}.md`),r.style.display="none",document.body.appendChild(r),r.click(),document.body.removeChild(r)};r()}catch(e){r(e)}}),1)},8594:(e,t,s)=>{s.d(t,{B:()=>w});var r=s(4848),n=s(6540),o=s(3),a=s(1319),i=s(9696),l=s(2090),c=s(5284),d=s(8834);function u({...e}){return(0,r.jsx)(d.bL,{"data-slot":"collapsible",...e})}function m({...e}){return(0,r.jsx)(d.R6,{"data-slot":"collapsible-trigger",...e})}function h({...e}){return(0,r.jsx)(d.Ke,{"data-slot":"collapsible-content",...e})}var p=s(1905),g=s(7736),f=s(6948);const x=e=>{const{children:t,className:s,wrapperClassName:a,buttonVariant:i="ghost",buttonClassName:d,...u}=e,[m,h]=(0,n.useState)(!1),[p,g]=(0,n.useState)(!1),f=n.Children.only(t);let x="";return f?.props?.children&&(x=Array.isArray(f.props.children)?f.props.children.map((e=>"string"==typeof e?e:"")).join(""):String(f.props.children),x=x.trim()),(0,r.jsxs)("div",{className:(0,c.cn)("relative my-4",a),onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),children:[(0,r.jsx)("pre",{className:(0,c.cn)("p-3 rounded-md overflow-x-auto thin-scrollbar",s),...u,children:t}),x&&(0,r.jsx)(l.$,{variant:i,size:"sm","aria-label":m?"Copied!":"Copy code",title:m?"Copied!":"Copy code",className:(0,c.cn)("absolute right-2 top-2 h-8 w-8 p-0","transition-opacity duration-200",p||m?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none",d),onClick:()=>{x&&(navigator.clipboard.writeText(x),h(!0),setTimeout((()=>h(!1)),1500))},children:m?(0,r.jsx)(o.YrT,{className:"h-4 w-4"}):(0,r.jsx)(o.nxz,{className:"h-4 w-4"})})]})},b=({content:e})=>{const[t,s]=(0,n.useState)(!1);return(0,r.jsx)("div",{className:"mb-2",children:(0,r.jsxs)(u,{open:t,onOpenChange:s,className:"w-full",children:[(0,r.jsx)(m,{asChild:!0,children:(0,r.jsx)(l.$,{variant:"outline",size:"sm",className:(0,c.cn)("mb-1","border-foreground text-foreground hover:text-accent-foreground"),children:t?"Hide Thoughts":"Show Thoughts"})}),(0,r.jsx)(h,{children:(0,r.jsx)("div",{className:(0,c.cn)("p-3 rounded-md border border-dashed","bg-muted","border-muted-foreground","text-muted-foreground"),children:(0,r.jsx)("div",{className:"markdown-body",children:(0,r.jsx)(a.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:v,children:e})})})})]})})},v={ul:({children:e,className:t,...s})=>(0,r.jsx)("ul",{className:(0,c.cn)("list-disc pl-5 my-2",t),...s,children:e}),ol:({children:e,className:t,...s})=>(0,r.jsx)("ol",{className:(0,c.cn)("list-decimal pl-5 my-2",t),...s,children:e}),p:({children:e,className:t,...s})=>(0,r.jsx)("p",{className:(0,c.cn)("mb-0",t),...s,children:e}),pre:x,code:e=>{const{children:t,className:s,inline:n,...o}=e;return n?(0,r.jsx)("code",{className:(0,c.cn)("px-1 py-0.5 rounded-sm bg-[var(--code-inline-bg)] text-[var(--code-inline-text)] text-sm",s),...o,children:t}):(0,r.jsx)("code",{className:(0,c.cn)("font-mono text-sm",s),...o,children:t})},a:({children:e,href:t,className:s,...n})=>(0,r.jsx)("a",{href:t,className:(0,c.cn)("text-[var(--link)] hover:underline",s),target:"_blank",rel:"noopener noreferrer",...n,children:e}),strong:({children:e,className:t,...s})=>(0,r.jsx)("strong",{className:(0,c.cn)("font-bold",t),...s,children:e}),em:({children:e,className:t,...s})=>(0,r.jsx)("em",{className:(0,c.cn)("italic",t),...s,children:e}),h1:({children:e,className:t,...s})=>(0,r.jsx)("h1",{className:(0,c.cn)("text-2xl font-bold mt-4 mb-2 border-b pb-1 border-[var(--border)]",t),...s,children:e}),h2:({children:e,className:t,...s})=>(0,r.jsx)("h2",{className:(0,c.cn)("text-xl font-semibold mt-3 mb-1 border-b pb-1 border-[var(--border)]",t),...s,children:e}),h3:({children:e,className:t,...s})=>(0,r.jsx)("h3",{className:(0,c.cn)("text-lg font-semibold mt-2 mb-1 border-b pb-1 border-[var(--border)]",t),...s,children:e}),table:({children:e,className:t,...s})=>(0,r.jsx)("div",{className:"markdown-table-wrapper my-2 overflow-x-auto",children:(0,r.jsx)("table",{className:(0,c.cn)("w-full border-collapse border border-[var(--border)]",t),...s,children:e})}),thead:({children:e,className:t,...s})=>(0,r.jsx)("thead",{className:(0,c.cn)("bg-[var(--muted)]",t),...s,children:e}),tbody:({children:e,className:t,...s})=>(0,r.jsx)("tbody",{className:(0,c.cn)(t),...s,children:e}),tr:e=>(0,r.jsx)("tr",{className:(0,c.cn)("border-b border-[var(--border)] even:bg-[var(--muted)]/50",e.className),...e}),th:({children:e,className:t,...s})=>(0,r.jsx)("th",{className:(0,c.cn)("p-2 border border-[var(--border)] text-left font-semibold",t),...s,children:e}),td:({children:e,className:t,...s})=>(0,r.jsx)("td",{className:(0,c.cn)("p-2 border border-[var(--border)]",t),...s,children:e}),blockquote:({children:e,className:t,...s})=>(0,r.jsx)("blockquote",{className:(0,c.cn)("pl-4 italic border-l-4 border-[var(--border)] my-2 text-[var(--muted-foreground)]",t),...s,children:e}),pre:e=>(0,r.jsx)(x,{...e,buttonVariant:"copy-button"})},y=({turn:e,index:t,isEditing:s,editText:d,onStartEdit:u,onSetEditText:m,onSaveEdit:h,onCancelEdit:x})=>{const{config:y}=(0,f.UK)(),w=(e.rawContent||"").split(/(<think>[\s\S]*?<\/think>)/g).filter((e=>e&&""!==e.trim())),j=/<think>([\s\S]*?)<\/think>/;return(0,n.useEffect)((()=>{if(!s)return;const e=e=>{"Escape"===e.key?(e.preventDefault(),e.stopPropagation(),x()):"Enter"!==e.key||e.shiftKey||e.altKey||e.metaKey||d.trim()&&(e.preventDefault(),e.stopPropagation(),h())};return document.addEventListener("keydown",e,!0),()=>{document.removeEventListener("keydown",e,!0)}}),[s,x,h,d]),(0,r.jsx)("div",{className:(0,c.cn)("text-base text-left relative","w-full px-4 py-3","assistant"===e.role?"text-foreground":"text-foreground/90","chatMessage",s?"editing":"",y&&"number"==typeof y.fontSize&&y.fontSize<=15?"font-semibold":""),onDoubleClick:()=>{s||u(t,e.rawContent)},children:s?(0,r.jsxs)("div",{className:"flex flex-col space-y-2 items-stretch w-full p-1",children:[(0,r.jsx)(i.T,{autosize:!0,value:d,onChange:e=>m(e.target.value),placeholder:"Edit your message...",className:(0,c.cn)("w-full rounded-md border bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground","border-input","text-foreground","hover:border-primary focus-visible:border-primary focus-visible:ring-0","min-h-[60px]"),minRows:3,autoFocus:!0}),(0,r.jsxs)("div",{className:"flex font-mono justify-end space-x-2",children:[(0,r.jsxs)(l.$,{size:"sm",variant:"outline",onClick:h,title:"Save changes",children:[(0,r.jsx)(o.YrT,{className:"h-4 w-4 mr-1"})," Save"]}),(0,r.jsxs)(l.$,{variant:"outline",size:"sm",onClick:x,title:"Discard changes",children:[(0,r.jsx)(o.yGN,{className:"h-4 w-4 mr-1"})," Exit"]})]})]}):(0,r.jsxs)("div",{className:"message-markdown markdown-body relative z-[1] text-foreground",children:[(0,r.jsx)("div",{className:(0,c.cn)("text-apple-caption1 font-medium mb-2","assistant"===e.role?"text-muted-foreground":"text-muted-foreground/80"),children:"assistant"===e.role?"Assistant":"You"}),"assistant"===e.role&&e.webDisplayContent&&(0,r.jsx)("div",{className:"message-prefix",children:(0,r.jsx)(a.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:v})}),w.map(((e,t)=>{const s=e.match(j);return s&&s[1]?(0,r.jsx)(b,{content:s[1]},`think_${t}`):(0,r.jsx)("div",{className:"message-content",children:(0,r.jsx)(a.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:v,children:e})},`content_${t}`)}))]})})},w=({turns:e=[],isLoading:t=!1,onReload:s=()=>{},settingsMode:a=!1,onEditTurn:i})=>{const[d,u]=(0,n.useState)(-1),[m,h]=(0,n.useState)(null),[p,g]=(0,n.useState)(""),{config:x}=(0,f.UK)(),b=(0,n.useRef)(null),v=(0,n.useRef)(null);(0,n.useLayoutEffect)((()=>{const e=v.current;e&&e.scrollHeight-e.scrollTop-e.clientHeight<200&&(e.scrollTop=e.scrollHeight)}),[e]);const w=e=>{navigator.clipboard.writeText(e)},j=(e,t)=>{h(e),g(t)},N=()=>{h(null),g("")},S=()=>{null!==m&&p.trim()&&i(m,p),N()};return(0,r.jsxs)("div",{ref:v,id:"messages",className:(0,c.cn)("flex flex-col flex-grow w-full overflow-y-auto pb-2 pt-2","no-scrollbar"),style:{background:"var(--bg)",opacity:a?0:1},children:[e.map(((t,n)=>t&&(0,r.jsxs)("div",{className:(0,c.cn)("flex items-start w-full relative group","border-b border-border/30 last:border-b-0",(t.role,"justify-start")),onMouseEnter:()=>u(n),onMouseLeave:()=>u(-1),children:["assistant"===t.role&&(0,r.jsxs)("div",{className:(0,c.cn)("flex flex-row items-center space-x-1 ml-2 transition-opacity duration-100",d===n?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:[m!==n&&(0,r.jsx)(l.$,{"aria-label":"Copy",variant:"message-action",size:"xs",onClick:()=>w(t.rawContent),title:"Copy message",children:(0,r.jsx)(o.nxz,{className:"text-[var(--text)]"})}),n===e.length-1&&(0,r.jsx)(l.$,{"aria-label":"Reload",variant:"message-action",size:"xs",onClick:s,title:"Reload last prompt",children:(0,r.jsx)(o.jEl,{className:"text-[var(--text)]"})})]}),(0,r.jsx)(y,{turn:t,index:n,isEditing:m===n,editText:p,onStartEdit:j,onSetEditText:g,onSaveEdit:S,onCancelEdit:N}),"user"===t.role&&(0,r.jsx)("div",{className:(0,c.cn)("flex flex-row items-center space-x-1 ml-2 transition-opacity duration-100",d===n?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:m!==n&&(0,r.jsx)(l.$,{"aria-label":"Copy",variant:"message-action",size:"xs",onClick:()=>w(t.rawContent),title:"Copy message",children:(0,r.jsx)(o.nxz,{className:"text-[var(--text)]"})})})]},t.timestamp||`turn_${n}`))),(0,r.jsx)("div",{ref:b,style:{height:"1px"}})]})}},8698:(e,t,s)=>{s.d(t,{N:()=>o});var r=s(6540),n=s(6948);const o=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),s=(0,r.useRef)(0),o=[{host:"ollama",isEnabled:e=>!!e.ollamaUrl&&!0===e.ollamaConnected,getUrl:e=>`${e.ollamaUrl}/api/tags`,parseFn:(e,t)=>(e?.models??[]).map((e=>({...e,id:e.id??e.name,host:t}))),onFetchFail:(e,t)=>t({ollamaConnected:!1,ollamaUrl:""})}];return{fetchAllModels:(0,r.useCallback)((async()=>{const r=Date.now();if(r-s.current<3e4)return void console.log("[useUpdateModels] Model fetch throttled");s.current=r;const n=e;if(!n)return void console.warn("[useUpdateModels] Config not available, skipping fetch.");console.log("[useUpdateModels] Starting model fetch for all configured services...");const a=await Promise.allSettled(o.map((async e=>{if(!e.isEnabled(n))return{host:e.host,models:[],status:"disabled"};const s=e.getUrl(n);if(!s)return console.warn(`[useUpdateModels] Could not determine URL for host: ${e.host}`),{host:e.host,models:[],status:"error",error:"Invalid URL"};const r=e.getFetchOptions?e.getFetchOptions(n):{},o=await(async(e,t={})=>{try{const s=await fetch(e,t);return s.ok?await s.json():void console.error(`[fetchDataSilently] HTTP error! Status: ${s.status} for URL: ${e}`)}catch(t){return void console.error(`[fetchDataSilently] Fetch or JSON parse error for URL: ${e}`,t)}})(s,r);if(o){const t=e.parseFn(o,e.host);return{host:e.host,models:t,status:"success"}}return e.onFetchFail&&e.onFetchFail(n,t),{host:e.host,models:[],status:"error",error:"Fetch failed"}})));let i=[];a.forEach((e=>{"fulfilled"===e.status&&"success"===e.value.status&&i.push(...e.value.models)}));const l=n.models||[],c={};((e,t)=>{if(e.length!==t.length)return!0;const s=(e,t)=>e.id.localeCompare(t.id),r=[...e].sort(s),n=[...t].sort(s);return JSON.stringify(r)!==JSON.stringify(n)})(i,l)&&(console.log("[useUpdateModels] Aggregated models changed. Updating config."),c.models=i);const d=n.selectedModel,u=c.models||l,m=d&&u.some((e=>e.id===d))?d:u[0]?.id;(m!==d||c.models)&&(c.selectedModel=m),Object.keys(c).length>0?t(c):console.log("[useUpdateModels] No changes to models or selectedModel needed."),console.log("[useUpdateModels] Model fetch cycle complete.")}),[e,t,3e4,o])}}},8971:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(6540),n=s(2951),o=s(5431);const a=1e3;var i=s(1100);try{const e=chrome.runtime.getURL("pdf.worker.mjs");e?i.EA.workerSrc=e:console.error("Failed to get URL for pdf.worker.mjs. PDF parsing might fail.")}catch(e){console.error("Error setting pdf.js worker source:",e)}const l=(e,t,s,l,c,d,u,m,h,p,g)=>{const f=(0,r.useRef)(null),x=(0,r.useRef)(null),b=(e,t,s,r,n)=>{if(f.current===e||s||r||n){if(null===f.current&&null!==e&&(""===t&&s&&!r&&!n||r&&(t.includes("Operation cancelled by user")||t.includes("Streaming operation cancelled"))))return console.log(`[${e}] updateAssistantTurn: Signal received after operation already finalized. Preserving existing state.`),p(!1),void g("idle");d((o=>{if(0===o.length||"assistant"!==o[o.length-1].role){if(console.warn(`[${e}] updateAssistantTurn: No assistant turn found or last turn is not assistant.`),r){const e={role:"assistant",rawContent:`Error: ${t||"Unknown operation error"}`,status:"error",timestamp:Date.now()};return[...o,e]}return o}const a=o[o.length-1],i=!0===r?"error":!0===n?"cancelled":s?"complete":"streaming";let l;if(n){const e=a.rawContent||"";l=e+(e?" ":"")+t}else l=r?`Error: ${t||"Unknown stream/handler error"}`:t;return[...o.slice(0,-1),{...a,rawContent:l,status:i,timestamp:Date.now()}]})),(s||!0===r||!0===n)&&(console.log(`[${e}] updateAssistantTurn: Final state (Finished: ${s}, Error: ${r}, Cancelled: ${n}). Clearing guard and loading.`),p(!1),g(r||n?"idle":"done"),f.current===e&&(f.current=null,x.current&&(x.current=null)))}else console.log(`[${e}] updateAssistantTurn: Guard mismatch (current: ${f.current}), skipping non-final update.`)};return{onSend:async t=>{const r=Date.now();console.log(`[${r}] useSendMessage: onSend triggered.`);const l=t||"";if(!c)return console.log(`[${r}] useSendMessage: Bailing out: Missing config.`),void p(!1);if(!l||!c)return void console.log(`[${r}] useSendMessage: Bailing out: Missing message or config.`);null!==f.current&&(console.warn(`[${r}] useSendMessage: Another send operation (ID: ${f.current}) is already in progress. Aborting previous.`),x.current&&x.current.abort());const v=new AbortController;x.current=v,console.log(`[${r}] useSendMessage: Setting loading true.`),p(!0),m(""),h("");const y=c.chatMode||"chat";g("web"===y?"searching":"page"===y?"reading":"thinking"),f.current=r;const w=l.match(/(https?:\/\/[^\s]+)/g);let j="";if(w&&w.length>0){g("searching");try{j=(await Promise.all(w.map((e=>(0,n.hj)(e,v.signal))))).map(((e,t)=>`Content from [${w[t]}]:\n${e}`)).join("\n\n")}catch(e){j="[Error scraping one or more URLs]"}g("thinking")}const N={role:"user",status:"complete",rawContent:l,timestamp:Date.now()};d((e=>[...e,N])),u(""),console.log(`[${r}] useSendMessage: User turn added to state.`);const S={role:"assistant",rawContent:"",status:"streaming",timestamp:Date.now()+1};d((e=>[...e,S])),console.log(`[${r}] useSendMessage: Assistant placeholder turn added early.`);let C=l,k="",$="";const M=c?.models?.find((e=>e.id===c.selectedModel));if(!M)return console.error(`[${r}] useSendMessage: No current model found.`),void b(r,"Configuration error: No model selected.",!0,!0);const E=void 0;{console.log(`[${r}] useSendMessage: Optimizing query...`),g("thinking");const e=s.map((e=>({role:e.role,content:e.rawContent})));try{const t=await(0,n.GW)(l,c,M,E,v.signal,e);t&&t.trim()&&t!==l?(C=t,$=`**Optimized query:** "*${C}*"\n\n`,console.log(`[${r}] useSendMessage: Query optimized to: "${C}"`)):($=`**Original query:** "${C}"\n\n`,console.log(`[${r}] useSendMessage: Using original query: "${C}"`))}catch(e){console.error(`[${r}] Query optimization failed:`,e),$=`**Fallback query:** "${C}"\n\n`}}console.log(`[${r}] useSendMessage: Performing web search...`),g("searching");try{if(k=await(0,n.tE)(C,c,v.signal),g("thinking"),v.signal.aborted)return void console.log(`[${r}] Web search was aborted (signal check post-await).`)}catch(e){if(console.error(`[${r}] Web search failed:`,e),"AbortError"===e.name||v.signal.aborted)return void console.log(`[${r}] Web search aborted. onStop handler will finalize UI.`);{k="";const t=`Web Search Failed: ${e instanceof Error?e.message:String(e)}`;return g("idle"),void b(r,t,!0,!0,!1)}}console.log(`[${r}] useSendMessage: Web search completed. Length: ${k.length}`),$&&d((e=>e.map((t=>"assistant"===t.role&&e[e.length-1]===t&&"complete"!==t.status&&"error"!==t.status&&"cancelled"!==t.status?{...t,webDisplayContent:$}:t))));const A=C,P=1e3*(c?.webLimit||1),z=P&&"string"==typeof k?k.substring(0,P):k,L=128===c?.webLimit?k:z,T=s.map((e=>({content:e.rawContent||"",role:e.role}))).concat({role:"user",content:l});let I="";if("page"===c?.chatMode){let e="";console.log(`[${r}] useSendMessage: Preparing page content...`),g("reading");try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(t?.url&&!t.url.startsWith("chrome://")){const s=t.url,n=t.mimeType;if(s.toLowerCase().endsWith(".pdf")||n&&"application/pdf"===n){console.log(`[${r}] Detected PDF URL: ${s}. Attempting to extract text.`);try{e=await async function(e,t){try{console.log(`[${t||"PDF"}] Attempting to fetch PDF from URL: ${e}`);const s=await fetch(e);if(!s.ok)throw new Error(`Failed to fetch PDF: ${s.status} ${s.statusText}`);const r=await s.arrayBuffer();console.log(`[${t||"PDF"}] PDF fetched, size: ${r.byteLength} bytes. Parsing...`);const n=await i.YE({data:r}).promise;console.log(`[${t||"PDF"}] PDF parsed. Number of pages: ${n.numPages}`);let o="";for(let e=1;e<=n.numPages;e++){const s=await n.getPage(e);o+=(await s.getTextContent()).items.map((e=>"str"in e?e.str:"")).join(" ")+"\n\n",e%10!=0&&e!==n.numPages||console.log(`[${t||"PDF"}] Extracted text from page ${e}/${n.numPages}`)}return console.log(`[${t||"PDF"}] PDF text extraction complete. Total length: ${o.length}`),o.trim()}catch(s){throw console.error(`[${t||"PDF"}] Error extracting text from PDF (${e}):`,s),s}}(s,r),console.log(`[${r}] Successfully extracted text from PDF. Length: ${e.length}`)}catch(t){console.error(`[${r}] Failed to extract text from PDF ${s}:`,t),e=`Error extracting PDF content: ${t instanceof Error?t.message:"Unknown PDF error"}. Falling back.`}}else console.log(`[${r}] URL is not a PDF. Fetching from storage: ${s}`),e=await o.A.getItem("pagestring")||"",console.log(`[${r}] Retrieved page text content from storage. Length: ${e.length}`)}else console.log(`[${r}] Not fetching page content for URL: ${t?.url} (might be chrome:// or no active tab).`)}catch(t){console.error(`[${r}] Error getting active tab or initial page processing:`,t),e=`Error accessing page content: ${t instanceof Error?t.message:"Unknown error"}`}const t=1e3*(c?.contextLimit||1),s="string"==typeof e?e:"",n=t&&s?s.substring(0,t):s;I=128===c?.contextLimit?s:n,h(I||""),g("thinking"),console.log(`[${r}] Page content prepared for LLM. Length: ${I?.length}`)}else h("");const O=c?.personas?.[c?.persona]||"",R="page"===c?.chatMode&&I?`Use the following page content for context: ${I}`:"",_=L?`Refer to this web search summary: ${L}`:"";let F="";const U=c.userName?.trim(),D=c.userProfile?.trim();U&&"user"!==U.toLowerCase()&&""!==U?(F=`You are interacting with a user named "${U}".`,D&&(F+=` Their provided profile information is: "${D}".`)):D&&(F=`You are interacting with a user. Their provided profile information is: "${D}".`);const q=[];O&&q.push(O),F&&q.push(F),R&&q.push(R),_&&q.push(_),j&&q.push(`Use the following scraped content from URLs in the user's message:\n${j}`);const W=q.join("\n\n").trim();console.log(`[${r}] useSendMessage: System prompt constructed. Persona: ${!!O}, UserCtx: ${!!F}, PageCtx: ${!!R}, WebCtx: ${!!_}, LinkCtx: ${!!j}`);try{if(g("thinking"),"high"===c?.computeLevel&&M)console.log(`[${r}] useSendMessage: Starting HIGH compute level.`),await(async(e,t,s,r,o,i,l)=>{const c=Math.max(.1,.5*(s.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into stages...",!1),d(),await new Promise((e=>setTimeout(e,a)));const u=`You are a planning agent. Given the original task: "${e}", break it down into the main sequential stages required to accomplish it. Output *only* a numbered list of stages. Example:\n1. First stage\n2. Second stage`,m=await(0,n.GW)(u,s,r,o,l,[],c),h=m.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(console.log("HighCompute - Raw L1 Decomposition Result:",m),i(`Monitoring: Generated Stages:\n${h.join("\n")||"[None]"}`,!1),d(),!h||0===h.length)return i("Error: Failed to decompose task into stages. Falling back to direct query.",!0),"Error: Could not decompose task.";const p=[];for(let t=0;t<h.length;t++){const u=h[t];d(),i(`Processing Stage ${t+1}/${h.length}: ${u}...`,!1);const m=`You are a planning agent. Given the stage: "${u}", break it down into the specific sequential steps needed to complete it. Output *only* a numbered list of steps. If no further breakdown is needed, output "No breakdown needed."`;d(),await new Promise((e=>setTimeout(e,a)));const g=await(0,n.GW)(m,s,r,o,l,[],c);console.log(`HighCompute - Raw L2 Decomposition Result (Stage ${t+1}):`,g);const f=g.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));i(`Monitoring: Generated Steps for Stage ${t+1}:\n${f.join("\n")||"[None, or direct solve]"}`,!1);let x="";if(0===f.length||g.includes("No breakdown needed")){i(`Solving Stage ${t+1} directly...`,!1),d();const c=`Complete the following stage based on the original task "${e}": "${u}"`;d(),await new Promise((e=>setTimeout(e,a))),x=await(0,n.GW)(c,s,r,o,l),console.log(`HighCompute - Raw Direct Solve Result (Stage ${t+1}):`,x),i(`Monitoring: Direct Solve Result for Stage ${t+1}:\n${x}`,!1)}else{const m=[],h=2;let p="";for(let c=0;c<f.length;c+=h){const g=f.slice(c,c+h),x=c/h+1;d(),i(`Solving Step Batch ${x} for Stage ${t+1}: ${g.join(", ")}...`,!1);const b=`You are an expert problem solver. Given the stage: "${u}" and the original task: "${e}", complete the following steps.  Consider the following accumulated context from previous steps: ${p}\n\n${g.map(((e,t)=>`${c+t+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the steps.`;d(),await new Promise((e=>setTimeout(e,a)));const v=await(0,n.GW)(b,s,r,o,l);console.log(`HighCompute - Raw Batch Results (Stage ${t+1}, Batch ${x}):`,v),i(`Monitoring: Raw Batch Results for Stage ${t+1}, Batch ${x}:\n${v}`,!1);const y=v.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));console.log(`HighCompute - Parsed Batch Results (Stage ${t+1}, Batch ${x}):`,y),i(`Monitoring: Parsed Batch Results for Stage ${t+1}, Batch ${x}:\n${y.join("\n")||"[None]"}`,!1);for(let e=0;e<y.length;e++){const t=y[e];m.push(t),p+=`Step ${c+e+1}: ${t}\n`}}i(`Synthesizing results for Stage ${t+1}...`,!1),d(),await new Promise((e=>setTimeout(e,a)));const g=`Synthesize the results of the following steps for stage "${u}" into a coherent paragraph:\n\n${m.map(((e,t)=>`Step ${t+1} Result:\n${e}`)).join("\n\n")}`;x=await(0,n.GW)(g,s,r,o,l,[],c),console.log(`HighCompute - Raw Stage Synthesis Result (Stage ${t+1}):`,x),i(`Monitoring: Synthesized Result for Stage ${t+1}:\n${x}`,!1)}p.push(x),i(`Monitoring: Accumulated Stage Results so far:\n${p.map(((e,t)=>`Stage ${t+1}: ${e}`)).join("\n---\n")}`,!1)}i("Synthesizing final answer...",!1),d();const g=`Based on the results of the following stages, provide a final comprehensive answer for the original task "${e}":\n\n${p.map(((e,t)=>`Stage ${t+1} (${h[t]}):\n${e}`)).join("\n\n")}`;i(`Monitoring: Final Synthesis Prompt:\n${g}`,!1),console.log("HighCompute - Final Synthesis Prompt:",g),d(),await new Promise((e=>setTimeout(e,a)));const f=await(0,n.GW)(g,s,r,o,l,[],c);console.log("HighCompute - Raw Final Synthesis Result:",f);const x="**High Compute Breakdown:**\n\n"+p.map(((e,t)=>`**Stage ${t+1}: ${h[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${f}`;return i(x,!0),x})(A,0,c,M,E,((e,t)=>b(r,e,Boolean(t))),v.signal),console.log(`[${r}] useSendMessage: HIGH compute level finished.`);else if("medium"===c?.computeLevel&&M)console.log(`[${r}] useSendMessage: Starting MEDIUM compute level.`),await(async(e,t,s,r,o,i,l)=>{const c=Math.max(.1,.5*(s.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into subtasks...",!1),d(),await new Promise((e=>setTimeout(e,a)));const u=`You are a planning agent. Given the task: "${e}", break it down into logical subtasks needed to accomplish it. Output *only* a numbered list of subtasks.`,m=await(0,n.GW)(u,s,r,o,l,[],c),h=m.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(console.log("MediumCompute - Raw Decomposition Result:",m),i(`Monitoring: Generated Subtasks:\n${h.join("\n")||"[None]"}`,!1),d(),!h||0===h.length){i("Warning: Failed to decompose into subtasks. Attempting direct query.",!1),d(),await new Promise((e=>setTimeout(e,a)));const t=await(0,n.GW)(e,s,r,o,l);return i(t,!0),t}const p=[];for(let t=0;t<h.length;t+=2){const c=h.slice(t,t+2),u=t/2+1;d(),i(`Solving Subtask Batch ${u}: ${c.join(", ")}...`,!1);const m=`You are an expert problem solver. Given the task: "${e}", complete the following subtasks:\n\n${c.map(((e,s)=>`${t+s+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the subtasks.`;d(),await new Promise((e=>setTimeout(e,a)));const g=await(0,n.GW)(m,s,r,o,l);console.log(`MediumCompute - Raw Batch Results (Batch ${u}):`,g),i(`Monitoring: Raw Batch Results for Batch ${u}:\n${g}`,!1);const f=g.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));console.log(`MediumCompute - Parsed Batch Results (Batch ${u}):`,f),i(`Monitoring: Parsed Batch Results for Batch ${u}:\n${f.join("\n")||"[None]"}`,!1);for(let e=0;e<f.length;e++)p.push(f[e])}i("Synthesizing final answer...",!1),d(),await new Promise((e=>setTimeout(e,a)));const g=`Synthesize the results of the following subtasks into a final comprehensive answer for the original task "${e}":\n\n${p.map(((e,t)=>`Subtask ${t+1} Result:\n${e}`)).join("\n\n")}`;console.log("MediumCompute - Final Synthesis Prompt:",g),i(`Monitoring: Final Synthesis Prompt:\n${g}`,!1);const f=await(0,n.GW)(g,s,r,o,l,[],c);console.log("MediumCompute - Raw Final Synthesis Result:",f);const x="**Medium Compute Breakdown:**\n\n"+p.map(((e,t)=>`**Subtask ${t+1}: ${h[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${f}`;return i(x,!0),x})(A,0,c,M,E,((e,t)=>b(r,e,Boolean(t))),v.signal),console.log(`[${r}] useSendMessage: MEDIUM compute level finished.`);else{console.log(`[${r}] useSendMessage: Starting standard streaming.`);const e={stream:!0},t={ollama:`${c?.ollamaUrl||""}/api/chat`}[M.host||""];if(!t)return void b(r,`Configuration error: Could not determine API URL for host '${M.host}'.`,!0,!0);const s=[];""!==W.trim()&&s.push({role:"system",content:W}),s.push(...T),console.log(`[${r}] useSendMessage: Sending chat request to ${t} with system prompt: "${W}"`),await(0,n.hL)(t,{...e,model:c?.selectedModel||"",messages:s,temperature:c?.temperature??.7,max_tokens:c?.maxTokens??32048,top_p:c?.topP??1,presence_penalty:c?.presencepenalty??0},((e,t,s)=>{b(r,e,Boolean(t),Boolean(s)),(t||s)&&console.log(`[${r}] fetchDataAsStream Callback: Stream finished/errored.`)}),E,M.host||"",v.signal),console.log(`[${r}] useSendMessage: fetchDataAsStream call INITIATED.`)}}catch(t){if(v.signal.aborted)console.log(`[${r}] Send operation was aborted. 'onStop' handler is responsible for UI updates.`),e&&p(!1),g("idle"),f.current===r&&(f.current=null),x.current&&x.current.signal===v.signal&&(x.current=null);else{console.error(`[${r}] useSendMessage: Error during send operation:`,t);const e=t instanceof Error?t.message:String(t);b(r,e,!0,!0)}}console.log(`[${r}] useSendMessage: onSend processing logic completed.`)},onStop:()=>{const e=f.current;null!==e?(console.log(`[${e}] useSendMessage: onStop triggered.`),x.current&&(x.current.abort(),x.current=null),b(e,"[Operation cancelled by user]",!0,!1,!0)):(console.log("[No CallID] useSendMessage: onStop triggered but no operation in progress."),p(!1),g("idle"))}}}},9018:(e,t,s)=>{s.d(t,{bq:()=>p,eb:()=>f,gC:()=>g,l6:()=>m,yv:()=>h});var r=s(4848),n=s(6540),o=s(854),a=s(5107),i=s(5773),l=s(2102),c=s(5284);const d={default:"bg-transparent data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs data-[size=sm]:h-8",settingsPanel:(0,c.cn)("text-[var(--text)] rounded-xl shadow-md","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98","bg-[var(--bg)] border border-[var(--text)]/20","backdrop-blur-sm","h-8"),settings:(0,c.cn)("text-[var(--text)] rounded-md shadow-md","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98","bg-[var(--bg)] border border-[var(--text)]/20","backdrop-blur-sm","h-8")},u={default:"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",settingsPanel:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto","bg-[var(--bg)] text-[var(--text)] border border-[var(--text)]/10","rounded-xl shadow-lg backdrop-blur-sm")};function m({...e}){return(0,r.jsx)(o.bL,{"data-slot":"select",...e})}function h({...e}){return(0,r.jsx)(o.WT,{"data-slot":"select-value",...e})}const p=n.forwardRef((({className:e,size:t="default",variant:s="default",children:n,...i},l)=>(0,r.jsxs)(o.l9,{ref:l,"data-slot":"select-trigger","data-size":t,className:(0,c.cn)("flex w-fit items-center justify-between gap-2 rounded-md border px-3 py-2 text-sm whitespace-nowrap transition-[color,box-shadow] outline-none disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",d[s],e),...i,children:[n,(0,r.jsx)(o.In,{asChild:!0,children:(0,r.jsx)(a.A,{className:"size-4 opacity-50"})})]})));p.displayName=o.l9.displayName;const g=n.forwardRef((({className:e,children:t,position:s="popper",variant:n="default",...a},i)=>(0,r.jsx)(o.ZL,{children:(0,r.jsxs)(o.UC,{ref:i,"data-slot":"select-content",className:(0,c.cn)(u[n],"default"===n&&"popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...a,children:[(0,r.jsx)(x,{}),(0,r.jsx)(o.LM,{className:(0,c.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(b,{})]})})));function f({className:e,children:t,focusVariant:s="default",...n}){return(0,r.jsxs)(o.q7,{"data-slot":"select-item",className:(0,c.cn)("[&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2","activeTheme"===s?"text-[var(--text)] hover:bg-[var(--active)]/10 focus:bg-[var(--active)]/20 focus:text-[var(--text)] data-[highlighted]:bg-[var(--active)]/15":"text-popover-foreground focus:bg-accent focus:text-accent-foreground",e),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(o.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(o.p4,{children:t})]})}function x({className:e,...t}){return(0,r.jsx)(o.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(l.A,{className:"size-4"})})}function b({className:e,...t}){return(0,r.jsx)(o.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(a.A,{className:"size-4"})})}g.displayName=o.UC.displayName},9696:(e,t,s)=>{s.d(t,{T:()=>a});var r=s(4848),n=(s(6540),s(1663)),o=s(5284);function a({className:e,autosize:t=!1,minRows:s,maxRows:a,style:i,...l}){return t?(0,r.jsx)(n.A,{minRows:s,maxRows:a,style:i,className:(0,o.cn)("flex w-full bg-transparent placeholder:text-muted-foreground","focus-visible:border-ring focus-visible:ring-ring/50","field-sizing-content text-sm md:text-sm transition-[color,box-shadow] outline-none focus-visible:ring-[3px]","disabled:cursor-not-allowed disabled:opacity-50","thin-scrollbar",e),...l}):(0,r.jsx)("textarea",{"data-slot":"textarea-default",className:(0,o.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...l})}},9828:(e,t,s)=>{s.a(e,(async(e,r)=>{try{s.d(t,{A:()=>L});var n=s(4848),o=s(6540),a=s(3790),i=s.n(a),l=s(5066),c=s(1735),d=s(9197),u=s(2090),m=s(3885),h=s(5284),p=s(9853),g=s(8971),f=s(8698),x=s(523),b=s(9861),v=s(6948),y=s(4156),w=s(1587),j=s(8594),N=s(7660),S=s(2823),C=s(5431),k=e([N]);function $(){let e="",t="",s="",r="",n="",o="",a="";try{e=document.title||"";const i=5e6;if(document.body,document.body&&document.body.innerHTML.length>i){console.warn(`[ChromePanion Bridge] Document body is very large (${document.body.innerHTML.length} chars). Attempting to use a cloned, simplified version for text extraction to improve performance/stability.`);const e=document.body.cloneNode(!0);e.querySelectorAll("script, style, noscript, iframe, embed, object").forEach((e=>e.remove())),t=(e.textContent||"").replace(/\s\s+/g," ").trim(),s=document.body.innerHTML.replace(/\s\s+/g," ")}else document.body?(t=(document.body.innerText||"").replace(/\s\s+/g," ").trim(),s=(document.body.innerHTML||"").replace(/\s\s+/g," ")):console.warn("[ChromePanion Bridge] document.body is not available.");r=Array.from(document.images).map((e=>e.alt)).filter((e=>e&&e.trim().length>0)).join(". "),n=Array.from(document.querySelectorAll("table")).map((e=>(e.innerText||"").replace(/\s\s+/g," "))).join("\n");const l=document.querySelector('meta[name="description"]');o=l&&l.getAttribute("content")||"";const c=document.querySelector('meta[name="keywords"]');a=c&&c.getAttribute("content")||""}catch(e){console.error("[ChromePanion Bridge] Error during content extraction:",e);let t="Unknown extraction error";return e instanceof Error?t=e.message:"string"==typeof e&&(t=e),JSON.stringify({error:`Extraction failed: ${t}`,title:document.title||"Error extracting title",text:"",html:"",altTexts:"",tableData:"",meta:{description:"",keywords:""}})}const i=1e7;let l={title:e,text:t,html:s,altTexts:r,tableData:n,meta:{description:o,keywords:a}};if(JSON.stringify(l).length>i){console.warn("[ChromePanion Bridge] Total extracted content is very large. Attempting to truncate.");const e=i-JSON.stringify({...l,text:"",html:""}).length;let t=e;l.text.length>.6*t&&(l.text=l.text.substring(0,Math.floor(.6*t))+"... (truncated)"),t=e-l.text.length,l.html.length>.8*t&&(l.html=l.html.substring(0,Math.floor(.8*t))+"... (truncated)"),console.warn("[ChromePanion Bridge] Content truncated. Final approx length:",JSON.stringify(l).length)}return JSON.stringify(l)}async function M(){const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(!e?.id||e.url?.startsWith("chrome://")||e.url?.startsWith("chrome-extension://")||e.url?.startsWith("about:"))return C.A.deleteItem("pagestring"),C.A.deleteItem("pagehtml"),C.A.deleteItem("alttexts"),void C.A.deleteItem("tabledata");C.A.deleteItem("pagestring"),C.A.deleteItem("pagehtml"),C.A.deleteItem("alttexts"),C.A.deleteItem("tabledata");try{const t=await chrome.scripting.executeScript({target:{tabId:e.id},func:$});if(!t||!Array.isArray(t)||0===t.length||!t[0]||"string"!=typeof t[0].result)return void console.error("[ChromePanion:] Bridge function execution returned invalid or unexpected results structure:",t);const s=t[0].result;let r;try{r=JSON.parse(s)}catch(e){return void console.error("[ChromePanion:] Failed to parse JSON result from bridge:",e,"Raw result string:",s)}if(r.error)return void console.error("[ChromePanion:] Bridge function reported an error:",r.error,"Title:",r.title);try{C.A.setItem("pagestring",r?.text??""),C.A.setItem("pagehtml",r?.html??""),C.A.setItem("alttexts",r?.altTexts??""),C.A.setItem("tabledata",r?.tableData??"")}catch(e){console.error("[ChromePanion:] Storage error after successful extraction:",e),C.A.deleteItem("pagestring"),C.A.deleteItem("pagehtml"),C.A.deleteItem("alttexts"),C.A.deleteItem("tabledata")}}catch(e){console.error("[ChromePanion:] Bridge function execution failed:",e),e instanceof Error&&(e.message.includes('Cannot access contents of url "chrome://')||e.message.includes("Cannot access a chrome extension URL")||e.message.includes('Cannot access contents of url "about:'))&&console.warn("[ChromePanion:] Cannot access restricted URL.")}}N=(k.then?(await k)():k)[0];const E=()=>`chat_${Math.random().toString(16).slice(2)}`,A=({children:e,onClick:t})=>(0,n.jsx)("div",{className:(0,h.cn)("bg-[var(--active)] border border-[var(--text)] rounded-[16px] text-[var(--text)]","cursor-pointer flex items-center justify-center","text-md font-extrabold p-0.5 place-items-center relative text-center","w-16 flex-shrink-0","transition-colors duration-200 ease-in-out","hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,children:e}),P=[{id:"Google",icon:d.DSS,label:"Google Search"}],z=({children:e,onClick:t,isActive:s,title:r})=>(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{children:(0,n.jsx)("div",{className:(0,h.cn)("border rounded-lg text-[var(--text)]","cursor-pointer flex items-center justify-center","p-2 place-items-center relative","w-8 h-8 flex-shrink-0","transition-colors duration-200 ease-in-out",s?"bg-[var(--active)] text-[var(--text)] border-[var(--active)] hover:brightness-95":"bg-transparent border-[var(--text)]/50 hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,"aria-label":r,children:e})}),(0,n.jsx)(m.ZI,{side:"top",className:"bg-[var(--active)]/80 text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:r})})]}),L=()=>{const[e,t]=(0,o.useState)([]),[s,r]=(0,o.useState)(""),[a,d]=(0,o.useState)(E()),[k,$]=(0,o.useState)(""),[L,T]=(0,o.useState)(""),[I,O]=(0,o.useState)(!1),[R,_]=(0,o.useState)(!1),[F,U]=(0,o.useState)(!1),{config:D,updateConfig:q}=(0,v.UK)(),[W,B]=(0,o.useState)({id:null,url:""}),G=(0,o.useRef)(null),H=(0,o.useRef)({id:null,url:""}),[J,K]=(0,o.useState)(!1),[Y,V]=(0,o.useState)(!1),[Z,Q]=(0,o.useState)("idle");(0,o.useEffect)((()=>{const e=new ResizeObserver((()=>{G.current&&(G.current.style.minHeight="100dvh",requestAnimationFrame((()=>{G.current&&(G.current.style.minHeight="")})))}));return G.current&&e.observe(G.current),()=>e.disconnect()}),[]),(0,o.useEffect)((()=>{if("page"!==D?.chatMode)return;const e=async()=>{const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(e?.id&&e.url)return e.url.startsWith("chrome://")||e.url.startsWith("chrome-extension://")||e.url.startsWith("about:")?(H.current.id===e.id&&H.current.url===e.url||(C.A.deleteItem("pagestring"),C.A.deleteItem("pagehtml"),C.A.deleteItem("alttexts"),C.A.deleteItem("tabledata")),H.current={id:e.id,url:e.url},void B({id:e.id,url:e.url})):void(e.id===H.current.id&&e.url===H.current.url||(H.current={id:e.id,url:e.url},B({id:e.id,url:e.url}),await M()))};e();const t=t=>{chrome.tabs.get(t.tabId,(t=>{chrome.runtime.lastError?console.warn(`[ChromePanion ] Error getting tab info on activation: ${chrome.runtime.lastError.message}`):e()}))},s=(t,s,r)=>{r.active&&("complete"===s.status||s.url&&"complete"===r.status)&&e()};return chrome.tabs.onActivated.addListener(t),chrome.tabs.onUpdated.addListener(s),()=>{chrome.tabs.onActivated.removeListener(t),chrome.tabs.onUpdated.removeListener(s),H.current={id:null,url:""}}}),[D?.chatMode]),(0,o.useEffect)((()=>{const e=e=>{};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[D?.chatMode,q,R,F]);const{chatTitle:X,setChatTitle:ee}=(0,p.S)(I,e,s),{onSend:te,onStop:se}=(0,g.A)(I,s,e,k,D,t,r,$,T,O,Q);(0,f.N)();const re=()=>{t([]),T(""),$(""),O(!1),q({chatMode:"web",computeLevel:"low"}),Q("idle"),r(""),ee(""),d(E()),U(!1),_(!1),G.current&&(G.current.scrollTop=0)},ne=async()=>{try{const t=(await i().keys()).filter((e=>e.startsWith("chat_")));if(0===t.length&&0===e.length)return;await Promise.all(t.map((e=>i().removeItem(e)))),console.log("[ChromePanion] Deleted all chats"),re()}catch(e){console.error("[ChromePanion] Error deleting all chats:",e)}};return(0,o.useEffect)((()=>{if(e.length>0&&!F&&!R){const t={id:a,title:X||`Chat ${new Date(Date.now()).toLocaleString()}`,turns:e,last_updated:Date.now(),model:D?.selectedModel,chatMode:D?.chatMode,webMode:"web"===D?.chatMode?D.webMode:void 0};i().setItem(a,t).catch((e=>{console.error(`[ChromePanion ] Error saving chat ${a}:`,e)}))}}),[a,e,X,D?.selectedModel,D?.chatMode,D?.webMode,F,R]),(0,o.useEffect)((()=>{if("done"===Z||"idle"===Z){const e=setTimeout((()=>{Q("idle")}),1500);return()=>clearTimeout(e)}}),[Z]),(0,o.useEffect)((()=>{let e=!1;return(async()=>{if(!e){re();try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});!e&&t?.id&&t.url?(B({id:t.id,url:t.url}),(t.url.startsWith("chrome://")||t.url.startsWith("chrome-extension://")||t.url.startsWith("about:"))&&(C.A.deleteItem("pagestring"),C.A.deleteItem("pagehtml"),C.A.deleteItem("alttexts"),C.A.deleteItem("tabledata"),H.current={id:null,url:""})):e||(H.current={id:null,url:""},B({id:null,url:""}),C.A.deleteItem("pagestring"),C.A.deleteItem("pagehtml"),C.A.deleteItem("alttexts"),C.A.deleteItem("tabledata"))}catch(t){e||(console.error("[ChromePanion - Revised] Error during panel open tab check:",t),H.current={id:null,url:""},B({id:null,url:""}),C.A.deleteItem("pagestring"),C.A.deleteItem("pagehtml"),C.A.deleteItem("alttexts"),C.A.deleteItem("tabledata"))}}})(),()=>{e=!0,C.A.deleteItem("pagestring"),C.A.deleteItem("pagehtml"),C.A.deleteItem("alttexts"),C.A.deleteItem("tabledata"),re(),H.current={id:null,url:""}}}),[]),(0,n.jsx)(m.Bc,{delayDuration:300,children:(0,n.jsxs)("div",{ref:G,className:(0,h.cn)("w-full h-dvh p-0 overflow-hidden","flex flex-col bg-[var(--bg)]"),children:[(0,n.jsx)(y.Y,{chatTitle:X,deleteAll:ne,downloadImage:()=>(0,N.GV)(e),downloadJson:()=>(0,N.xD)(e),downloadText:()=>(0,N.mR)(e),downloadMarkdown:()=>(0,N.ii)(e),historyMode:F,reset:re,setHistoryMode:U,setSettingsMode:_,settingsMode:R,chatMode:D?.chatMode||"chat",chatStatus:Z}),(0,n.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 no-scrollbar overflow-y-auto relative",children:[R&&(0,n.jsx)(S.w,{}),!R&&F&&(0,n.jsx)(b.D,{className:"flex-1 w-full min-h-0",loadChat:e=>{ee(e.title||""),t(e.turns),d(e.id),U(!1),Q("idle"),_(!1),"page"!==e.chatMode&&(C.A.deleteItem("pagestring"),C.A.deleteItem("pagehtml"),C.A.deleteItem("alttexts"),C.A.deleteItem("tabledata"),H.current={id:null,url:""})},onDeleteAll:ne}),!R&&!F&&(0,n.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 relative",children:[(0,n.jsx)(j.B,{isLoading:I,turns:e,settingsMode:R,onReload:()=>{t((e=>{if(e.length<2)return e;const t=e[e.length-1],s=e[e.length-2];return"assistant"===t.role&&"user"===s.role?(r(s.rawContent),e.slice(0,-2)):e})),O(!1),Q("idle")},onEditTurn:(e,s)=>{t((t=>{const r=[...t];return r[e]&&(r[e]={...r[e],rawContent:s}),r}))}}),0===e.length&&!D?.chatMode&&(0,n.jsxs)("div",{className:"fixed bottom-20 left-4 flex flex-col gap-2 z-[5] bg-card/80 backdrop-blur-md p-2 rounded-xl border border-border/50 shadow-lg",children:[(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{asChild:!0,children:(0,n.jsx)(u.$,{"aria-label":"Cycle compute level",variant:"ghost",size:"icon",onClick:()=>{const e=D.computeLevel;q({computeLevel:"low"===e?"medium":"medium"===e?"high":"low"})},className:(0,h.cn)("hover:bg-secondary/70","high"===D.computeLevel?"text-red-600":"medium"===D.computeLevel?"text-orange-300":"text-[var(--text)]"),children:(0,n.jsx)(c.cfR,{})})}),(0,n.jsx)(m.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)] max-w-80",children:(0,n.jsx)("p",{children:`Compute Level: ${D.computeLevel?.toUpperCase()}. Click to change. [Warning]: beta feature and resource costly.`})})]}),(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{asChild:!0,children:(0,n.jsx)(u.$,{"aria-label":"Add Web Search Results to LLM Context",variant:"ghost",size:"icon",onClick:()=>{q({chatMode:"web",webMode:D.webMode||P[0].id})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,n.jsx)(l.pqQ,{})})}),(0,n.jsx)(m.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,n.jsx)("p",{children:"Add Web Search Results to LLM Context"})})]}),(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{asChild:!0,children:(0,n.jsx)(u.$,{"aria-label":"Add Current Web Page to LLM Context",variant:"ghost",size:"icon",onClick:()=>{q({chatMode:"page"})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,n.jsx)(l.RGv,{})})}),(0,n.jsx)(m.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,n.jsx)("p",{children:"Add Current Web Page to LLM Context"})})]})]}),"page"===D?.chatMode&&(0,n.jsx)("div",{className:(0,h.cn)("fixed bottom-16 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-8 z-[2]","transition-all duration-200 ease-in-out","opacity-100 translate-y-0","bg-card/80 backdrop-blur-md px-3 py-2 rounded-xl border border-border/50 shadow-lg"),onMouseEnter:()=>K(!0),onMouseLeave:()=>K(!1),children:(0,n.jsxs)("div",{className:"flex items-center space-x-6 max-w-full overflow-x-auto px-0",children:[(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{children:(0,n.jsx)(A,{onClick:()=>te("Provide your summary."),children:"TLDR"})}),(0,n.jsx)(m.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Quick Summary"})})]}),(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{children:(0,n.jsx)(A,{onClick:()=>te("Extract all key figures, names, locations, and dates mentioned on this page and list them."),children:"Facts"})}),(0,n.jsx)(m.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Numbers, events, names"})})]}),(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{children:(0,n.jsx)(A,{onClick:()=>te("Find positive developments, achievements, or opportunities mentioned on this page."),children:"Yay!"})}),(0,n.jsx)(m.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Good news"})})]}),(0,n.jsxs)(m.m_,{children:[(0,n.jsx)(m.k$,{children:(0,n.jsx)(A,{onClick:()=>te("Find concerning issues, risks, or criticisms mentioned on this page."),children:"Oops"})}),(0,n.jsx)(m.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Bad news"})})]})]})}),"web"===D?.chatMode&&(0,n.jsx)("div",{className:(0,h.cn)("fixed bottom-14 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-10 z-[2]","transition-all duration-200 ease-in-out","opacity-100 translate-y-0","bg-card/80 backdrop-blur-md px-3 py-2 rounded-xl border border-border/50 shadow-lg"),onMouseEnter:()=>V(!0),onMouseLeave:()=>V(!1),children:(0,n.jsx)("div",{className:"flex items-center space-x-4 max-w-full overflow-x-auto px-4 py-1",children:P.map((e=>(0,n.jsx)(z,{onClick:()=>{q({webMode:e.id,chatMode:"web"})},isActive:D.webMode===e.id,title:e.label,children:(0,n.jsx)(e.icon,{size:18})},e.id)))})})]})]}),!R&&!F&&(0,n.jsx)("div",{className:"p-2 relative z-[10]",children:(0,n.jsx)(w.p,{isLoading:I,message:s,setMessage:r,onSend:()=>te(s),onStopRequest:se})}),D?.backgroundImage?(0,n.jsx)(x.V,{}):null]})})};r()}catch(T){r(T)}}))},9853:(e,t,s)=>{s.d(t,{S:()=>i});var r=s(6540),n=s(6948),o=s(2951);const a=e=>e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/"/g,"").replace(/#/g,"").trim().split(/\s+/).slice(0,4).join(" ")||"New Chat",i=(e,t,s)=>{const[i,l]=(0,r.useState)(""),{config:c}=(0,n.UK)(),d=(0,r.useRef)(null);return(0,r.useEffect)((()=>{if(!e&&t.length>=2&&!i&&c?.generateTitle){d.current&&d.current.abort(),d.current=new AbortController;const e=d.current.signal,s=c?.models?.find((e=>e.id===c.selectedModel));if(!s)return;const r=[...t.slice(0,2).map((e=>({content:e.rawContent||"",role:e.role}))),{role:"user",content:"Create a short 2-4 word title for this chat. Keep it concise, just give me the best one, just one. No explanations or thinking steps needed."}],n=(()=>{const e={body:{model:s.id,messages:r,stream:!["ollama"].includes(s.host||"")},headers:{}};if("ollama"===s.host)return{...e,url:`${c.ollamaUrl}/api/chat`}})();if(!n)return;const i=t=>{e.aborted?console.log("Title generation aborted."):console.error("Title generation failed:",t)};if(["ollama"].includes(s.host||""))fetch(n.url,{method:"POST",headers:{"Content-Type":"application/json",...n.headers},body:JSON.stringify(n.body),signal:e}).then((e=>e.json())).then((e=>{const t=e.choices?.[0]?.message?.content||"",s=a(t);s&&(console.log("Setting chat title (local):",s),l(s))})).catch(i);else{let t="";(0,o.hL)(n.url,n.body,((s,r)=>{if(t=s,e.aborted)console.log("Title streaming aborted during callback.");else if(r){const e=a(t);e&&(console.log("Setting chat title (streaming):",e),l(e))}}),n.headers,s.host||"",e)}}return()=>{d.current&&(d.current.abort(),d.current=null)}}),[e,t,s,c,i]),{chatTitle:i,setChatTitle:l}}},9861:(e,t,s)=>{s.d(t,{D:()=>x});var r=s(4848),n=s(6540),o=s(7211),a=s(2090),i=s(6627),l=s(5284);function c({className:e,children:t,viewportRef:s,...n}){return(0,r.jsxs)(i.bL,{"data-slot":"scroll-area",className:(0,l.cn)("relative",e),...n,children:[(0,r.jsx)(i.LM,{ref:s,"data-slot":"scroll-area-viewport",className:(0,l.cn)("size-full rounded-[inherit]","focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:shadow-none","[&>div]:!border-b-0","pb-px pr-px"),children:t}),(0,r.jsx)(d,{orientation:"vertical"}),(0,r.jsx)(d,{orientation:"horizontal"}),(0,r.jsx)(i.OK,{})]})}function d({className:e,orientation:t="vertical",...s}){return(0,r.jsx)(i.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-px","horizontal"===t&&"h-px w-full border-b-0 bg-transparent shadow-none min-h-0",e),...s,children:(0,r.jsx)(i.lr,{"data-slot":"scroll-area-thumb",className:"relative flex-1 rounded-sm"})})}var u=s(6250),m=s(3790),h=s.n(m),p=s(6532);const g=e=>new Date(e).toLocaleDateString("sv-SE"),f=12,x=({loadChat:e,onDeleteAll:t,className:s})=>{const[i,l]=(0,n.useState)([]),[d,m]=(0,n.useState)(""),[x,b]=(0,n.useState)(1),[v,y]=(0,n.useState)(null),[w,j]=(0,n.useState)(null),N=(0,n.useCallback)((e=>{const t=e.sort(((e,t)=>t.last_updated-e.last_updated));l(t)}),[]);(0,n.useEffect)((()=>{(async()=>{try{const e=(await h().keys()).filter((e=>e.startsWith("chat_")));if(0===e.length)return l([]),void b(1);const t=e.map((e=>h().getItem(e))),s=(await Promise.all(t)).filter((e=>null!==e&&"object"==typeof e&&"id"in e&&"last_updated"in e));N(s),b(1)}catch(e){console.error("Error fetching messages:",e),l([])}})()}),[N]);const S=(0,n.useMemo)((()=>{if(!d)return i;const e=d.toLowerCase();return i.filter((t=>{const s=t.title?.toLowerCase().includes(e),r=t.turns.some((t=>t.rawContent.toLowerCase().includes(e)));return s||r}))}),[i,d]);(0,n.useEffect)((()=>{b(1)}),[d]);const C=(0,n.useMemo)((()=>Math.max(1,Math.ceil(S.length/f))),[S]);(0,n.useEffect)((()=>{x>C&&b(C)}),[x,C]);const k=(0,n.useMemo)((()=>{const e=(x-1)*f,t=e+f;return S.slice(e,t)}),[S,x]),$=(0,n.useMemo)((()=>k.map((e=>({...e,date:g(e.last_updated)})))),[k]),M=(0,n.useMemo)((()=>Array.from(new Set($.map((e=>e.date))))),[$]),E=(0,n.useCallback)((async e=>{try{await h().removeItem(e);const t=(await h().keys()).filter((e=>e.startsWith("chat_"))),s=(await Promise.all(t.map((e=>h().getItem(e))))).filter((e=>e&&"object"==typeof e&&"id"in e&&"last_updated"in e&&"turns"in e));N(s);const r=s.filter((e=>{if(!d)return!0;const t=d.toLowerCase(),s=e.title?.toLowerCase().includes(t),r=e.turns.some((e=>e.rawContent.toLowerCase().includes(t)));return s||r})),n=Math.max(1,Math.ceil(r.length/f));let o=x;o>n&&(o=n);const a=(o-1)*f;0===r.slice(a,a+f).length&&o>1&&(o-=1),b(o)}catch(e){console.error("Error deleting message:",e)}}),[N,x,d]),A=(0,n.useCallback)((async()=>{try{const e=(await h().keys()).filter((e=>e.startsWith("chat_")));await Promise.all(e.map((e=>h().removeItem(e)))),l([]),t&&t()}catch(e){console.error("Error deleting all messages:",e)}}),[t]);(0,n.useEffect)((()=>(window.deleteAllChats=A,()=>{window.deleteAllChats===A&&delete window.deleteAllChats})),[A]);const P=(0,n.useCallback)((()=>b((e=>Math.min(e+1,C)))),[C]),z=(0,n.useCallback)((()=>b((e=>Math.max(e-1,1)))),[]),L=`flex flex-col w-full ${s||""}`.trim(),T=e=>{m(e.target.value)};return 0!==i.length||d?0===S.length&&d?(0,r.jsxs)("div",{className:L,children:[(0,r.jsx)("div",{className:"p-0",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:d,onChange:T,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,r.jsx)(u.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,r.jsx)(c,{className:"flex-1 w-full min-h-0",children:(0,r.jsxs)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:['No results found for "',d,'".']})})]}):(0,r.jsxs)("div",{className:L,children:[(0,r.jsx)("div",{className:"p-0",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:d,onChange:T,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,r.jsx)(u.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,r.jsx)(c,{className:"flex-1 w-full min-h-0",children:(0,r.jsx)("div",{className:"px-4 pb-4 font-['Space_Mono',_monospace]",children:M.map((t=>(0,r.jsxs)("div",{className:"mb-3 mt-3",children:[(0,r.jsx)("p",{className:"text-foreground text-lg font-bold overflow-hidden pl-4 pb-1 text-left text-ellipsis whitespace-nowrap w-[90%]",children:t===g(new Date)?"Today":t}),$.filter((e=>e.date===t)).map((t=>(0,r.jsxs)("div",{className:"flex items-center group font-['Space_Mono',_monospace]",onMouseEnter:()=>y(t.id),onMouseLeave:()=>y(null),children:[(0,r.jsxs)("span",{className:"text-foreground text-base font-normal pl-4 w-[4.5rem] flex-shrink-0 font-['Space_Mono',_monospace]",children:[new Date(t.last_updated).getHours().toString().padStart(2,"0"),":",new Date(t.last_updated).getMinutes().toString().padStart(2,"0")]}),(0,r.jsx)("button",{className:`text-foreground text-base font-normal overflow-hidden px-4 py-2 text-left text-ellipsis whitespace-nowrap flex-grow hover:underline hover:underline-offset-4 hover:decoration-1 ${t.id===w?"line-through decoration-2":""} font-['Space_Mono',_monospace]`,onClick:()=>e(t),children:t.title||"Untitled Chat"}),(0,r.jsx)(o.P.div,{className:"shrink-0 transition-opacity duration-150 "+(v===t.id?"opacity-100":"opacity-0 group-hover:opacity-100"),whileHover:{rotate:"15deg"},onMouseEnter:()=>j(t.id),onMouseLeave:()=>j(null),children:(0,r.jsx)(a.$,{variant:"ghost",size:"sm","aria-label":"Delete chat",className:"rounded-full w-8 h-8 font-['Space_Mono',_monospace]",onClick:e=>{e.stopPropagation(),E(t.id)},children:(0,r.jsx)(u.ttk,{className:"h-4 w-4 text-foreground"})})})]},t.id)))]},t)))})}),C>1&&(0,r.jsxs)("div",{className:"flex justify-center items-center h-10 space-x-2 p-2 border-t border-[var(--active)]/50 font-['Space_Mono',_monospace]",children:[(0,r.jsx)(a.$,{onClick:z,disabled:1===x,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Prev"}),(0,r.jsxs)("span",{className:"text-md",children:["Page ",x," of ",C]}),(0,r.jsx)(a.$,{onClick:P,disabled:x===C,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Next"})]})]}):(0,r.jsxs)("div",{className:L,children:[(0,r.jsx)("div",{className:"p-0",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:d,onChange:T,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,r.jsx)(u.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,r.jsx)(c,{className:"flex-1 w-full min-h-0",children:(0,r.jsx)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:"No chat history found."})})]})}}},l={};function c(e){var t=l[e];if(void 0!==t)return t.exports;var s=l[e]={exports:{}};return i[e].call(s.exports,s,s.exports,c),s.exports}c.m=i,e="function"==typeof Symbol?Symbol("webpack queues"):"__webpack_queues__",t="function"==typeof Symbol?Symbol("webpack exports"):"__webpack_exports__",s="function"==typeof Symbol?Symbol("webpack error"):"__webpack_error__",r=e=>{e&&e.d<1&&(e.d=1,e.forEach((e=>e.r--)),e.forEach((e=>e.r--?e.r++:e())))},c.a=(n,o,a)=>{var i;a&&((i=[]).d=-1);var l,c,d,u=new Set,m=n.exports,h=new Promise(((e,t)=>{d=t,c=e}));h[t]=m,h[e]=e=>(i&&e(i),u.forEach(e),h.catch((e=>{}))),n.exports=h,o((n=>{var o;l=(n=>n.map((n=>{if(null!==n&&"object"==typeof n){if(n[e])return n;if(n.then){var o=[];o.d=0,n.then((e=>{a[t]=e,r(o)}),(e=>{a[s]=e,r(o)}));var a={};return a[e]=e=>e(o),a}}var i={};return i[e]=e=>{},i[t]=n,i})))(n);var a=()=>l.map((e=>{if(e[s])throw e[s];return e[t]})),c=new Promise((t=>{(o=()=>t(a)).r=0;var s=e=>e!==i&&!u.has(e)&&(u.add(e),e&&!e.d&&(o.r++,e.push(o)));l.map((t=>t[e](s)))}));return o.r?c:a()}),(e=>(e?d(h[s]=e):c(m),r(i)))),i&&i.d<0&&(i.d=0)},n=[],c.O=(e,t,s,r)=>{if(!t){var o=1/0;for(d=0;d<n.length;d++){for(var[t,s,r]=n[d],a=!0,i=0;i<t.length;i++)(!1&r||o>=r)&&Object.keys(c.O).every((e=>c.O[e](t[i])))?t.splice(i--,1):(a=!1,r<o&&(o=r));if(a){n.splice(d--,1);var l=s();void 0!==l&&(e=l)}}return e}r=r||0;for(var d=n.length;d>0&&n[d-1][2]>r;d--)n[d]=n[d-1];n[d]=[t,s,r]},c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},a=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var s=Object.create(null);c.r(s);var r={};o=o||[null,a({}),a([]),a(a)];for(var n=2&t&&e;"object"==typeof n&&!~o.indexOf(n);n=a(n))Object.getOwnPropertyNames(n).forEach((t=>r[t]=()=>e[t]));return r.default=()=>e,c.d(s,r),s},c.d=(e,t)=>{for(var s in t)c.o(t,s)&&!c.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},c.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=524,(()=>{var e={524:0};c.O.j=t=>0===e[t];var t=(t,s)=>{var r,n,[o,a,i]=s,l=0;if(o.some((t=>0!==e[t]))){for(r in a)c.o(a,r)&&(c.m[r]=a[r]);if(i)var d=i(c)}for(t&&t(s);l<o.length;l++)n=o[l],c.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return c.O(d)},s=globalThis.webpackChunkchromepanion=globalThis.webpackChunkchromepanion||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))})(),c.nc=void 0;var d=c.O(void 0,[465],(()=>c(3003)));d=c.O(d)})();